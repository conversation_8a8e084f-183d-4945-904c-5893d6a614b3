import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  ScrollView,
  Platform
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ContinueButton from '../components/ContinueButton';
import CountryCode from '../components/CountryCode';
import awsApi from '../services/aws-api';
import { getSessionToken, validateSession } from '../backend/userService';
import { saveUserId } from '../utils/userIdStorage';



const PhoneNumberScreen = () => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [warningMessage, setWarningMessage] = useState('');
  const navigation = useNavigation();
  const scrollViewRef = useRef(null);

  // Background session validation state
  const [hasCheckedForToken, setHasCheckedForToken] = useState(false);
  const [isTokenValid, setIsTokenValid] = useState(false);
  const [shouldStopChecking, setShouldStopChecking] = useState(false);

  // Refs for cleanup
  const sessionCheckInterval = useRef(null);
  const isMounted = useRef(true);

  // Validate phone number prefix as user types
  const validatePhoneNumberPrefix = (number) => {
    const trimmedNumber = number.trim();

    // Only check prefix if user has entered at least 2 digits
    if (trimmedNumber.length >= 2) {
      // Check if number starts with 81 or 85
      if (!trimmedNumber.startsWith('81') && !trimmedNumber.startsWith('85')) {
        return 'Phone number must start with 81 or 85.';
      }
    }

    return ''; // No warning for valid prefix or too short numbers
  };

  // Background session validation function
  const checkSessionInBackground = async () => {
    //console.log('🔍 PhoneNumberScreen: === BACKGROUND SESSION CHECK STARTED ===');
    //console.log('🔍 PhoneNumberScreen: isMounted.current:', isMounted.current);
    //console.log('🔍 PhoneNumberScreen: shouldStopChecking:', shouldStopChecking);

    if (!isMounted.current || shouldStopChecking) {
      //console.log('🔍 PhoneNumberScreen: Stopping check - component unmounted or should stop');
      return;
    }

    try {
      //console.log('🔍 PhoneNumberScreen: Checking for existing session token...');
      const token = await getSessionToken();
      //console.log('🔍 PhoneNumberScreen: Token result:', token ? `Found token: ${token.substring(0, 20)}...` : 'No token found');

      if (!token) {
        //console.log('🔍 PhoneNumberScreen: ❌ No session token found, stopping background checks permanently');
        setHasCheckedForToken(true);
        setShouldStopChecking(true);
        return;
      }

      //console.log('🔍 PhoneNumberScreen: Session token found, validating with backend...');
      const { valid, userId } = await validateSession(token);
      //console.log('🔍 PhoneNumberScreen: Validation result - valid:', valid, 'userId:', userId);

      if (!isMounted.current) {
        //console.log('🔍 PhoneNumberScreen: Component unmounted during validation, stopping');
        return;
      }

      if (valid) {
        //console.log('🔍 PhoneNumberScreen: ✅ Session is valid! Navigating to Feed...');
        setIsTokenValid(true);
        setShouldStopChecking(true);

        // Save userId before navigating
        if (userId) {
          //console.log('🔍 PhoneNumberScreen: Saving userId:', userId);
          await saveUserId(userId);
        }

        // Navigate to Feed screen
        //console.log('🔍 PhoneNumberScreen: 🚀 Navigating to Feed screen...');
        navigation.reset({ index: 0, routes: [{ name: 'Feed' }] });
      } else {
        //console.log('🔍 PhoneNumberScreen: ❌ Session is definitively invalid, stopping background checks permanently');
        setHasCheckedForToken(true);
        setShouldStopChecking(true);
      }
    } catch (error) {
      //console.log('🔍 PhoneNumberScreen: ⚠️ Session check failed (network/API error):', error.message);
      //console.log('🔍 PhoneNumberScreen: Error details:', error);

      if (!isMounted.current || shouldStopChecking) {
        //console.log('🔍 PhoneNumberScreen: Not retrying - component unmounted or should stop');
        return;
      }

      //console.log('🔍 PhoneNumberScreen: 🔄 Network/API error - retrying in 3 seconds (token exists, just can\'t reach server)...');
      // Only retry on network/API errors - we have a token but can't validate it due to connectivity
      sessionCheckInterval.current = setTimeout(() => {
        if (isMounted.current && !shouldStopChecking) {
          //console.log('🔍 PhoneNumberScreen: ⏰ Retry timeout triggered, checking again...');
          checkSessionInBackground();
        } else {
          //console.log('🔍 PhoneNumberScreen: ⏰ Retry timeout triggered but conditions changed, not retrying');
        }
      }, 2500);
    }
  };

  // Reset state flags when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      //console.log('🔍 PhoneNumberScreen: 📱 Screen focused, resetting background check state');
      setHasCheckedForToken(false);
      setIsTokenValid(false);
      setShouldStopChecking(false);
      isMounted.current = true;

      // Start background session check immediately
      //console.log('🔍 PhoneNumberScreen: 🚀 Starting initial background session check...');
      checkSessionInBackground();

      // Also set up a periodic check every 5 seconds to handle network reconnection
      const periodicCheck = setInterval(() => {
        if (isMounted.current && !shouldStopChecking) {
          //console.log('🔍 PhoneNumberScreen: ⏰ Periodic check triggered...');
          checkSessionInBackground();
        } else {
          //console.log('🔍 PhoneNumberScreen: ⏰ Periodic check skipped - conditions not met');
        }
      }, 3500);

      return () => {
        //console.log('🔍 PhoneNumberScreen: 📱 Screen unfocused, cleaning up');
        isMounted.current = false;
        setShouldStopChecking(true);

        // Clear both timeout and interval
        if (sessionCheckInterval.current) {
          clearTimeout(sessionCheckInterval.current);
          sessionCheckInterval.current = null;
        }
        clearInterval(periodicCheck);
        //console.log('🔍 PhoneNumberScreen: 🧹 All timers cleared');
      };
    }, [])
  );

  // Cleanup on unmount
  useEffect(() => {
    //console.log('🔍 PhoneNumberScreen: 🏗️ Component mounted, setting up cleanup');
    return () => {
      //console.log('🔍 PhoneNumberScreen: 🏗️ Component unmounting, final cleanup');
      isMounted.current = false;
      if (sessionCheckInterval.current) {
        clearTimeout(sessionCheckInterval.current);
        sessionCheckInterval.current = null;
        //console.log('🔍 PhoneNumberScreen: 🧹 Final timeout cleanup completed');
      }
    };
  }, []);

  const handleContinue = async () => {
    const trimmedNumber = phoneNumber.trim();

    // Validate phone number length (only show this warning on Continue button click)
    if (!trimmedNumber || trimmedNumber.length <= 8) {
      setWarningMessage('Please enter a valid phone number (e.g. 811234567).');
      return;
    }

    // Validate phone number format (must start with 81 or 85)
    if (!trimmedNumber.startsWith('81') && !trimmedNumber.startsWith('85')) {
      setWarningMessage('Phone number must start with 81 or 85.');
      return;
    }

    // Clear previous warning messages
    setWarningMessage('');

    // Construct the full phone number with country code
    // Assuming +264 is the fixed country code here
    const fullPhoneNumber = `+264${trimmedNumber}`;

    setLoading(true); // Start loading indicator
    
    try {
      console.log(`Checking phone number: ${fullPhoneNumber}`);
      // Call the AWS backend API
      const userExists = await awsApi.checkPhoneNumber(fullPhoneNumber);
      console.log(`User exists result: ${userExists}`);

      // Navigate based on the API response
      if (userExists) {
        console.log('Navigating to EnterCodeScreen');
        // Pass the phone number for the next screen to use
        navigation.navigate('EnterCode', { phoneNumber: fullPhoneNumber });
      } else {
        console.log('Navigating to RegisterScreen');
        // Pass the phone number for the next screen to use
        navigation.navigate('Register', { phoneNumber: fullPhoneNumber });
      }

    } catch (error) {
      // Handle API errors
      console.error('Error during phone check:', error);
      setWarningMessage(error.message || 'Could not check phone number. Please try again.');
    } finally {
      setLoading(false); // Stop loading indicator regardless of outcome
    }
  };

  const handleGuestLogin = async () => {
    setLoading(true);

    try {
      console.log('🔍 Guest Login: Checking for existing guest ID...');

      // Check if there's an existing guest ID in AsyncStorage
      const existingGuestId = await AsyncStorage.getItem('guestID');
      console.log('🔍 Guest Login: Existing guest ID:', existingGuestId ? `Found: ${existingGuestId.substring(0, 20)}...` : 'None found');

      let guestId;

      if (existingGuestId) {
        // Guest ID exists, verify it with the backend
        console.log('🔍 Guest Login: Verifying existing guest ID...');
        const verifyResult = await awsApi.guestVerify(existingGuestId);

        if (verifyResult.success) {
          guestId = verifyResult.guestId;
          console.log('✅ Guest Login: Guest ID verified successfully:', guestId);

          // Update AsyncStorage if the backend returned a different ID
          if (guestId !== existingGuestId) {
            console.log('🔄 Guest Login: Updating guest ID in AsyncStorage');
            await AsyncStorage.setItem('guestID', guestId);
          }
        } else {
          console.error('❌ Guest Login: Guest verification failed:', verifyResult.error);
          throw new Error('Failed to verify guest ID');
        }
      } else {
        // No guest ID exists, register a new one
        console.log('🔍 Guest Login: No existing guest ID, registering new guest...');
        const registerResult = await awsApi.guestRegister();

        if (registerResult.success) {
          guestId = registerResult.guestId;
          console.log('✅ Guest Login: New guest registered successfully:', guestId);

          // Save the new guest ID to AsyncStorage
          await AsyncStorage.setItem('guestID', guestId);
          console.log('💾 Guest Login: Guest ID saved to AsyncStorage');
        } else {
          console.error('❌ Guest Login: Guest registration failed:', registerResult.error);
          throw new Error('Failed to register new guest');
        }
      }

      // Navigate to GuestFeed with the guest ID
      console.log('🚀 Guest Login: Navigating to GuestFeed...');
      navigation.navigate('GuestFeed', { guestId });

    } catch (error) {
      console.error('❌ Guest Login Error:', error);
      Alert.alert(
        'Guest Login Error',
        'Unable to access guest mode. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
    >
      <ScrollView
        ref={scrollViewRef}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={true}
      >
        <View style={styles.welcomeContainer}>
          <Text style={styles.welcomeText}>Welcome to,</Text>
          <Text style={styles.omatalaText}>Omatala</Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.label}>Phone number:</Text>
          <View style={styles.phoneInputContainer}>
            <CountryCode style={styles.countryCode} />
            <TextInput
              style={styles.phoneInput}
              value={phoneNumber}
              onChangeText={(text) => {
                setPhoneNumber(text);
                // Show prefix validation warnings as user types
                const warning = validatePhoneNumberPrefix(text);
                setWarningMessage(warning);
              }}
              onFocus={() => {
                // Scroll to make the input visible when focused
                setTimeout(() => {
                  scrollViewRef.current?.scrollTo({ y: 100, animated: true });
                }, 100);
              }}
              placeholder="81 123 4567"
              keyboardType="phone-pad"
              autoFocus
              maxLength={9}
            />
          </View>

          {warningMessage ? (
            <Text style={styles.errorText}>{warningMessage}</Text>
          ) : null}

          <ContinueButton
            onPress={handleContinue}
            loading={loading}
            disabled={loading || !phoneNumber.trim()}
            style={styles.continueButton}
          />
        </View>

        <View style={styles.guestContainer}>
          <Text style={styles.guestText}>I am just a:</Text>
          <TouchableOpacity onPress={handleGuestLogin} disabled={loading}>
            <Text style={styles.guestLink}>GUEST</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
    justifyContent: 'space-between',
  },
  welcomeContainer: {
    marginTop: 60,
    marginBottom: 40,
  },
  welcomeText: {
    fontSize: 24,
    color: '#000000',
  },
  omatalaText: {
    fontSize: 40,
    color: '#FF6A00',
    fontWeight: 'bold',
    marginTop: 5,
  },
  formContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    paddingTop: 20,
    paddingBottom: 50,
  },
  label: {
    fontSize: 16,
    color: '#000000',
    marginBottom: 10,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  countryCode: {
    marginRight: 10,
  },
  phoneInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#FF6A00',
    borderRadius: 25,
    paddingHorizontal: 15,
    paddingVertical: 10,
    fontSize: 16,
    height: 50,
  },
  errorText: {
    color: 'red',
    marginBottom: 10,
    minHeight: 20,
  },
  continueButton: {
    marginTop: 10,
  },
  guestContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 40,
  },
  guestText: {
    fontSize: 16,
    color: '#000000',
  },
  guestLink: {
    fontSize: 16,
    color: '#FF6A00',
    fontWeight: 'bold',
    marginLeft: 5,
    textDecorationLine: 'underline',
  },
});

export default PhoneNumberScreen;