output "users_table_name" {
  description = "Name of the Users DynamoDB table"
  value       = aws_dynamodb_table.users_table.name
}

output "sessions_table_name" {
  description = "Name of the Sessions DynamoDB table"
  value       = aws_dynamodb_table.sessions_table.name
}

output "verification_codes_table_name" {
  description = "Name of the Verification Codes DynamoDB table"
  value       = aws_dynamodb_table.verification_codes_table.name
}

output "verification_codes_recycle_table_name" {
  description = "Name of the Verification Codes Recycle DynamoDB table"
  value       = aws_dynamodb_table.verification_codes_recycle_table.name
}

output "phone_number_index_name" {
  description = "Name of the PhoneNumberIndex GSI on the Users table"
  value       = "PhoneNumberIndex"
}

output "stall_name_index_name" {
  description = "Name of the StallNameIndex GSI on the Users table"
  value       = "StallNameIndex"
}

output "user_id_index_name" {
  description = "Name of the UserIdIndex GSI on the Sessions table"
  value       = "UserIdIndex"
}

output "deleted_accounts_table_name" {
  description = "Name of the Deleted Accounts DynamoDB table"
  value       = aws_dynamodb_table.deleted_accounts_table.name
}

output "deleted_accounts_phone_index_name" {
  description = "Name of the PhoneNumberIndex GSI on the Deleted Accounts table"
  value       = "PhoneNumberIndex"
}

output "items_table_name" {
  description = "Name of the Items DynamoDB table"
  value       = aws_dynamodb_table.items_table.name
}

output "items_user_id_index_name" {
  description = "Name of the UserIdIndex GSI on the Items table"
  value       = "UserIdIndex"
}

output "categories_table_name" {
  description = "Name of the Categories DynamoDB table"
  value       = aws_dynamodb_table.categories_table.name
}

output "items_category_index_name" {
  description = "Name of the CategoryIndex GSI on the Items table"
  value       = "CategoryIndex"
}

output "items_region_index_name" {
  description = "Name of the RegionIndex GSI on the Items table"
  value       = "RegionIndex"
}

output "items_visibility_index_name" {
  description = "Name of the VisibilityIndex GSI on the Items table"
  value       = "VisibilityIndex"
}

output "notifications_table_name" {
  description = "Name of the Notifications DynamoDB table"
  value       = aws_dynamodb_table.notifications_table.name
}

output "notifications_user_id_index_name" {
  description = "Name of the UserIdIndex GSI on the Notifications table"
  value       = "UserIdIndex"
}

output "guests_table_name" {
  description = "Name of the Guests DynamoDB table"
  value       = aws_dynamodb_table.guests_table.name
}

output "notifications_user_id_is_read_index_name" {
  description = "Name of the UserIdIsReadIndex GSI on the Notifications table"
  value       = "UserIdIsReadIndex"
}
