resource "aws_api_gateway_rest_api" "api" {
  name        = "${var.project_name}-api-${var.environment}"
  description = "API Gateway for ${var.project_name} ${var.environment} environment"

  endpoint_configuration {
    types = ["REGIONAL"]
  }
}

# Enable CORS for the API Gateway
resource "aws_api_gateway_resource" "cors_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "cors_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.cors_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "cors_integration" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.cors_resource.id
  http_method = aws_api_gateway_method.cors_method.http_method
  type        = "MOCK"
  
  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

resource "aws_api_gateway_method_response" "cors_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.cors_resource.id
  http_method = aws_api_gateway_method.cors_method.http_method
  status_code = "200"
  
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.cors_resource.id
  http_method = aws_api_gateway_method.cors_method.http_method
  status_code = aws_api_gateway_method_response.cors_method_response.status_code
  
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'GET,POST,PUT,DELETE,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }
}

# Custom authorizer for the API Gateway - commented out since we're using custom auth
# Keeping the resource name for reference
# resource "aws_api_gateway_authorizer" "cognito_authorizer" {
#   name          = "custom_authorizer_${var.environment}"
#   rest_api_id   = aws_api_gateway_rest_api.api.id
#   type          = "TOKEN"
#   authorizer_uri = var.custom_authorizer_uri
#   # This ensures the authorizer is created after the API Gateway
#   depends_on = [aws_api_gateway_rest_api.api]
# }

# Deployment of the API Gateway
resource "aws_api_gateway_deployment" "api_deployment" {
  depends_on = [
    aws_api_gateway_integration.cors_integration,
    aws_api_gateway_integration.check_phone_options,
    aws_api_gateway_integration.send_code_options,
    aws_api_gateway_integration.verify_code_options,
    aws_api_gateway_integration.check_pending_code_options,
    aws_api_gateway_integration.register_user_options,
    aws_api_gateway_integration.check_session_options,
    aws_api_gateway_integration.get_stall_data_options,
    aws_api_gateway_integration.add_item_options,
    aws_api_gateway_integration.update_item_options,
    aws_api_gateway_integration.update_user_settings_options,
    aws_api_gateway_integration.remove_profile_picture_options,
    aws_api_gateway_integration.guest_register_options,
    aws_api_gateway_integration.guest_verify_options
    # Note: Lambda integrations are created in the Lambda module
  ]

  rest_api_id = aws_api_gateway_rest_api.api.id

  # Force new deployment when endpoints change
  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_resource.check_phone.id,
      aws_api_gateway_resource.send_code.id,
      aws_api_gateway_resource.verify_code.id,
      aws_api_gateway_resource.check_pending_code.id,
      aws_api_gateway_resource.register_user.id,
      aws_api_gateway_resource.check_session.id,
      aws_api_gateway_resource.get_stall_data.id,
      aws_api_gateway_resource.add_item.id,
      aws_api_gateway_resource.update_item.id,
      aws_api_gateway_resource.update_user_settings.id,
      aws_api_gateway_resource.remove_profile_picture.id,
      aws_api_gateway_resource.guest_register.id,
      aws_api_gateway_resource.guest_verify.id,
      aws_api_gateway_method.check_phone_post.id,
      aws_api_gateway_method.send_code_post.id,
      aws_api_gateway_method.verify_code_post.id,
      aws_api_gateway_method.check_pending_code_post.id,
      aws_api_gateway_method.register_user_post.id,
      aws_api_gateway_method.check_session_post.id,
      aws_api_gateway_method.get_stall_data_get.id,
      aws_api_gateway_method.add_item_post.id,
      aws_api_gateway_method.update_item_post.id,
      aws_api_gateway_method.update_user_settings_post.id,
      aws_api_gateway_method.remove_profile_picture_post.id,
      aws_api_gateway_method.guest_register_post.id,
      aws_api_gateway_method.guest_verify_post.id,
      timestamp()
    ]))
  }

  # Remove stage_name and use aws_api_gateway_stage instead
  lifecycle {
    create_before_destroy = true
  }
}

# Import existing authentication resources (created manually in AWS Console)
# These resources already exist and will be imported into Terraform state

# Import check-phone resource (ID: 0cwea1)
resource "aws_api_gateway_resource" "check_phone" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "check-phone"
}

# Import send-code resource (ID: 8r4ujg)
resource "aws_api_gateway_resource" "send_code" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "send-code"
}

# Import verify-code resource (ID: ln7elj)
resource "aws_api_gateway_resource" "verify_code" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "verify-code"
}

# Check pending code resource
resource "aws_api_gateway_resource" "check_pending_code" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "check-pending-code"
}

# Import register-user resource (ID: nzxaxh)
resource "aws_api_gateway_resource" "register_user" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "register-user"
}

# Import check-session resource (need to find ID)
resource "aws_api_gateway_resource" "check_session" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "check-session"
}

# Create get-stall-data resource
resource "aws_api_gateway_resource" "get_stall_data" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "get-stall-data"
}

# Create add-item resource
resource "aws_api_gateway_resource" "add_item" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "add-item"
}

# Create update-item resource
resource "aws_api_gateway_resource" "update_item" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "update-item"
}

# Create update-user-settings resource
resource "aws_api_gateway_resource" "update_user_settings" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "update-user-settings"
}

# Create remove-profile-picture resource
resource "aws_api_gateway_resource" "remove_profile_picture" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "remove-profile-picture"
}

# Create guest-register resource
resource "aws_api_gateway_resource" "guest_register" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "guest-register"
}

# Create guest-verify resource
resource "aws_api_gateway_resource" "guest_verify" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "guest-verify"
}

# Create recycle-verification-code resource
resource "aws_api_gateway_resource" "recycle_verification_code" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "recycle-verification-code"
}

# Create POST method for check-phone
resource "aws_api_gateway_method" "check_phone_post" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.check_phone.id
  http_method   = "POST"
  authorization = "NONE"
}

# Create POST method for send-code
resource "aws_api_gateway_method" "send_code_post" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.send_code.id
  http_method   = "POST"
  authorization = "NONE"
}

# Create POST method for verify-code
resource "aws_api_gateway_method" "verify_code_post" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.verify_code.id
  http_method   = "POST"
  authorization = "NONE"
}

# Create POST method for check-pending-code
resource "aws_api_gateway_method" "check_pending_code_post" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.check_pending_code.id
  http_method   = "POST"
  authorization = "NONE"
}

# Create POST method for register-user
resource "aws_api_gateway_method" "register_user_post" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.register_user.id
  http_method   = "POST"
  authorization = "NONE"
}

# Create POST method for check-session
resource "aws_api_gateway_method" "check_session_post" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.check_session.id
  http_method   = "POST"
  authorization = "NONE"
}

# Create GET method for get-stall-data
resource "aws_api_gateway_method" "get_stall_data_get" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.get_stall_data.id
  http_method   = "GET"
  authorization = "NONE"
}

# Create POST method for add-item
resource "aws_api_gateway_method" "add_item_post" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.add_item.id
  http_method   = "POST"
  authorization = "NONE"
}

# Create POST method for update-item
resource "aws_api_gateway_method" "update_item_post" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.update_item.id
  http_method   = "POST"
  authorization = "NONE"
}

# Create POST method for update-user-settings
resource "aws_api_gateway_method" "update_user_settings_post" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.update_user_settings.id
  http_method   = "POST"
  authorization = "NONE"
}

# Create POST method for remove-profile-picture
resource "aws_api_gateway_method" "remove_profile_picture_post" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.remove_profile_picture.id
  http_method   = "POST"
  authorization = "NONE"
}

# Create POST method for guest-register
resource "aws_api_gateway_method" "guest_register_post" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.guest_register.id
  http_method   = "POST"
  authorization = "NONE"
}

# Create POST method for guest-verify
resource "aws_api_gateway_method" "guest_verify_post" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.guest_verify.id
  http_method   = "POST"
  authorization = "NONE"
}

# Create POST method for recycle-verification-code
resource "aws_api_gateway_method" "recycle_verification_code_post" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.recycle_verification_code.id
  http_method   = "POST"
  authorization = "NONE"
}

# Create OPTIONS methods for CORS
resource "aws_api_gateway_method" "check_phone_options" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.check_phone.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_method" "send_code_options" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.send_code.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_method" "verify_code_options" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.verify_code.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_method" "check_pending_code_options" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.check_pending_code.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_method" "register_user_options" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.register_user.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_method" "check_session_options" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.check_session.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

# Create OPTIONS method for get-stall-data (CORS)
resource "aws_api_gateway_method" "get_stall_data_options" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.get_stall_data.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

# Create OPTIONS method for add-item (CORS)
resource "aws_api_gateway_method" "add_item_options" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.add_item.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

# Create OPTIONS method for update-item (CORS)
resource "aws_api_gateway_method" "update_item_options" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.update_item.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

# Create OPTIONS method for update-user-settings (CORS)
resource "aws_api_gateway_method" "update_user_settings_options" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.update_user_settings.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

# Create OPTIONS method for remove-profile-picture (CORS)
resource "aws_api_gateway_method" "remove_profile_picture_options" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.remove_profile_picture.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

# Create OPTIONS method for guest-register (CORS)
resource "aws_api_gateway_method" "guest_register_options" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.guest_register.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

# Create OPTIONS method for guest-verify (CORS)
resource "aws_api_gateway_method" "guest_verify_options" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.guest_verify.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

# Create OPTIONS method for recycle-verification-code (CORS)
resource "aws_api_gateway_method" "recycle_verification_code_options" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.recycle_verification_code.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

# Note: Lambda integration will be created in the Lambda module to avoid circular dependency

# CORS integrations for OPTIONS methods
resource "aws_api_gateway_integration" "check_phone_options" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.check_phone.id
  http_method = aws_api_gateway_method.check_phone_options.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

resource "aws_api_gateway_integration" "send_code_options" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.send_code.id
  http_method = aws_api_gateway_method.send_code_options.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

resource "aws_api_gateway_integration" "verify_code_options" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.verify_code.id
  http_method = aws_api_gateway_method.verify_code_options.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

resource "aws_api_gateway_integration" "check_pending_code_options" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.check_pending_code.id
  http_method = aws_api_gateway_method.check_pending_code_options.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

resource "aws_api_gateway_integration" "register_user_options" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.register_user.id
  http_method = aws_api_gateway_method.register_user_options.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

resource "aws_api_gateway_integration" "check_session_options" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.check_session.id
  http_method = aws_api_gateway_method.check_session_options.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

# Integration for OPTIONS method (CORS)
resource "aws_api_gateway_integration" "get_stall_data_options" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.get_stall_data.id
  http_method = aws_api_gateway_method.get_stall_data_options.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

# Integration for add-item OPTIONS method (CORS)
resource "aws_api_gateway_integration" "add_item_options" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.add_item.id
  http_method = aws_api_gateway_method.add_item_options.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

# Integration for update-item OPTIONS method (CORS)
resource "aws_api_gateway_integration" "update_item_options" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.update_item.id
  http_method = aws_api_gateway_method.update_item_options.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

# Integration for update-user-settings OPTIONS method (CORS)
resource "aws_api_gateway_integration" "update_user_settings_options" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.update_user_settings.id
  http_method = aws_api_gateway_method.update_user_settings_options.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

# Integration for remove-profile-picture OPTIONS method (CORS)
resource "aws_api_gateway_integration" "remove_profile_picture_options" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.remove_profile_picture.id
  http_method = aws_api_gateway_method.remove_profile_picture_options.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

# Integration for guest-register OPTIONS method (CORS)
resource "aws_api_gateway_integration" "guest_register_options" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.guest_register.id
  http_method = aws_api_gateway_method.guest_register_options.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

# Integration for guest-verify OPTIONS method (CORS)
resource "aws_api_gateway_integration" "guest_verify_options" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.guest_verify.id
  http_method = aws_api_gateway_method.guest_verify_options.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

# Integration for recycle-verification-code OPTIONS method (CORS)
resource "aws_api_gateway_integration" "recycle_verification_code_options" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.recycle_verification_code.id
  http_method = aws_api_gateway_method.recycle_verification_code_options.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

# Method responses for OPTIONS methods
resource "aws_api_gateway_method_response" "check_phone_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.check_phone.id
  http_method = aws_api_gateway_method.check_phone_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_method_response" "send_code_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.send_code.id
  http_method = aws_api_gateway_method.send_code_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_method_response" "verify_code_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.verify_code.id
  http_method = aws_api_gateway_method.verify_code_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_method_response" "check_pending_code_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.check_pending_code.id
  http_method = aws_api_gateway_method.check_pending_code_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_method_response" "register_user_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.register_user.id
  http_method = aws_api_gateway_method.register_user_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_method_response" "check_session_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.check_session.id
  http_method = aws_api_gateway_method.check_session_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

# Method response for OPTIONS (CORS)
resource "aws_api_gateway_method_response" "get_stall_data_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.get_stall_data.id
  http_method = aws_api_gateway_method.get_stall_data_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

# Method response for add-item OPTIONS (CORS)
resource "aws_api_gateway_method_response" "add_item_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.add_item.id
  http_method = aws_api_gateway_method.add_item_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

# Method response for update-item OPTIONS (CORS)
resource "aws_api_gateway_method_response" "update_item_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.update_item.id
  http_method = aws_api_gateway_method.update_item_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

# Method response for update-user-settings OPTIONS (CORS)
resource "aws_api_gateway_method_response" "update_user_settings_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.update_user_settings.id
  http_method = aws_api_gateway_method.update_user_settings_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

# Method response for remove-profile-picture OPTIONS (CORS)
resource "aws_api_gateway_method_response" "remove_profile_picture_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.remove_profile_picture.id
  http_method = aws_api_gateway_method.remove_profile_picture_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_method_response" "guest_register_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.guest_register.id
  http_method = aws_api_gateway_method.guest_register_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_method_response" "guest_verify_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.guest_verify.id
  http_method = aws_api_gateway_method.guest_verify_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

# Method response for recycle-verification-code OPTIONS (CORS)
resource "aws_api_gateway_method_response" "recycle_verification_code_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.recycle_verification_code.id
  http_method = aws_api_gateway_method.recycle_verification_code_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}



# Integration response for OPTIONS (CORS)
resource "aws_api_gateway_integration_response" "get_stall_data_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.get_stall_data.id
  http_method = aws_api_gateway_method.get_stall_data_options.http_method
  status_code = aws_api_gateway_method_response.get_stall_data_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Session-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'GET,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }

  depends_on = [aws_api_gateway_integration.get_stall_data_options]
}

# Integration response for add-item OPTIONS (CORS)
resource "aws_api_gateway_integration_response" "add_item_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.add_item.id
  http_method = aws_api_gateway_method.add_item_options.http_method
  status_code = aws_api_gateway_method_response.add_item_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }

  depends_on = [aws_api_gateway_integration.add_item_options]
}

# Integration response for update-item OPTIONS (CORS)
resource "aws_api_gateway_integration_response" "update_item_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.update_item.id
  http_method = aws_api_gateway_method.update_item_options.http_method
  status_code = aws_api_gateway_method_response.update_item_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }

  depends_on = [aws_api_gateway_integration.update_item_options]
}

resource "aws_api_gateway_integration_response" "guest_register_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.guest_register.id
  http_method = aws_api_gateway_method.guest_register_options.http_method
  status_code = aws_api_gateway_method_response.guest_register_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }
}

resource "aws_api_gateway_integration_response" "guest_verify_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.guest_verify.id
  http_method = aws_api_gateway_method.guest_verify_options.http_method
  status_code = aws_api_gateway_method_response.guest_verify_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }
}

# Integration response for update-user-settings OPTIONS (CORS)
resource "aws_api_gateway_integration_response" "update_user_settings_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.update_user_settings.id
  http_method = aws_api_gateway_method.update_user_settings_options.http_method
  status_code = aws_api_gateway_method_response.update_user_settings_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }

  depends_on = [aws_api_gateway_integration.update_user_settings_options]
}

# Integration response for remove-profile-picture OPTIONS (CORS)
resource "aws_api_gateway_integration_response" "remove_profile_picture_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.remove_profile_picture.id
  http_method = aws_api_gateway_method.remove_profile_picture_options.http_method
  status_code = aws_api_gateway_method_response.remove_profile_picture_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }

  depends_on = [aws_api_gateway_integration.remove_profile_picture_options]
}

# Integration responses for other OPTIONS methods
resource "aws_api_gateway_integration_response" "check_phone_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.check_phone.id
  http_method = aws_api_gateway_method.check_phone_options.http_method
  status_code = aws_api_gateway_method_response.check_phone_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }

  depends_on = [aws_api_gateway_integration.check_phone_options]
}

resource "aws_api_gateway_integration_response" "send_code_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.send_code.id
  http_method = aws_api_gateway_method.send_code_options.http_method
  status_code = aws_api_gateway_method_response.send_code_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }

  depends_on = [aws_api_gateway_integration.send_code_options]
}

resource "aws_api_gateway_integration_response" "verify_code_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.verify_code.id
  http_method = aws_api_gateway_method.verify_code_options.http_method
  status_code = aws_api_gateway_method_response.verify_code_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }

  depends_on = [aws_api_gateway_integration.verify_code_options]
}

resource "aws_api_gateway_integration_response" "check_pending_code_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.check_pending_code.id
  http_method = aws_api_gateway_method.check_pending_code_options.http_method
  status_code = aws_api_gateway_method_response.check_pending_code_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }

  depends_on = [aws_api_gateway_integration.check_pending_code_options]
}

resource "aws_api_gateway_integration_response" "register_user_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.register_user.id
  http_method = aws_api_gateway_method.register_user_options.http_method
  status_code = aws_api_gateway_method_response.register_user_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }

  depends_on = [aws_api_gateway_integration.register_user_options]
}

resource "aws_api_gateway_integration_response" "check_session_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.check_session.id
  http_method = aws_api_gateway_method.check_session_options.http_method
  status_code = aws_api_gateway_method_response.check_session_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }

  depends_on = [aws_api_gateway_integration.check_session_options]
}

resource "aws_api_gateway_integration_response" "recycle_verification_code_options_200" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.recycle_verification_code.id
  http_method = aws_api_gateway_method.recycle_verification_code_options.http_method
  status_code = aws_api_gateway_method_response.recycle_verification_code_options_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }

  depends_on = [aws_api_gateway_integration.recycle_verification_code_options]
}

# Create a stage for the API Gateway
resource "aws_api_gateway_stage" "api_stage" {
  # Add lifecycle block to prevent conflicts
  lifecycle {
    create_before_destroy = true
    # Remove ignore_changes to allow deployment updates
  }
  deployment_id = aws_api_gateway_deployment.api_deployment.id
  rest_api_id   = aws_api_gateway_rest_api.api.id
  stage_name    = var.environment

  # Enable CloudWatch logging
  access_log_settings {
    destination_arn = var.cloudwatch_log_group_arn
    format = jsonencode({
      requestId        = "$context.requestId"
      ip               = "$context.identity.sourceIp"
      caller           = "$context.identity.caller"
      user             = "$context.identity.user"
      requestTime      = "$context.requestTime"
      httpMethod       = "$context.httpMethod"
      resourcePath     = "$context.resourcePath"
      status           = "$context.status"
      protocol         = "$context.protocol"
      responseLength   = "$context.responseLength"
    })
  }

  # Enable detailed CloudWatch metrics
  variables = {
    environment = var.environment
  }

  # Enable X-Ray tracing
  xray_tracing_enabled = true

  depends_on = [
    aws_api_gateway_deployment.api_deployment
  ]
}