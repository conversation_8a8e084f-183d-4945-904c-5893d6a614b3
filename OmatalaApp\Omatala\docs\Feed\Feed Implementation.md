# Marketplace Feed Implementation Guide

This document provides a complete implementation guide for building a fair distribution feed system for your marketplace app using a caching strategy. The system ensures all items are seen by users before their sellers' subscriptions expire, handles dynamic item on/off switching, and provides fair redistribution when all items have been viewed. This approach uses background caching to solve performance issues while maintaining real-time responsiveness.

## Understanding the Fair Distribution Algorithm

### Core Principle: Minimum Intersection Strategy

The algorithm is designed around one key principle: **minimize the overlap of unseen items across users in each batch** { Every item must be seen at least once (global goal), Every user must see every item (individual goal), System adapts   when users are inactive (dynamic rebalancing),Minimum intersection applies throughout (not just initially) }

### How It Works: Step-by-Step Example

**Setup:**
- Items: [A, B, C, D, E] 
- Users: U1, U2
- Pagination: 2 items per batch

**Phase 1: Initial Distribution (Ensuring Each Item Seen At Least Once)**

**Batch 1:**
- U1 gets: [A, B]
- U2 gets: [C, D] 
- Reasoning: Zero intersection between batches, items A,B,C,D are now "seen at least once"

**Batch 2:**
- U1 gets: [C, E] (needs C,D,E - gets C,E)
- U2 gets: [A, E] (needs A,B,E - gets A,E) 
- Reasoning: Intersection is only [E], which is unavoidable since E is the last item needing "at least once" exposure

**Current State:**
- All items [A,B,C,D,E] have been seen at least once ✓
- U1 still needs: [D]
- U2 still needs: [B]

**Batch 3:**
- U1 gets: [D] 
- U2 gets: [B]
- Result: Both users have now seen all items → "You're all caught up"

### Phase 2: New Items Join Mid-Session

**Scenario:** New item F appears while users are mid-session

**Priority Logic:**
1. **Check subscription urgency:** Does F have fewer subscription days than unseen items?
2. **If YES:** F gets priority insertion
3. **If NO:** F joins the queue after current unseen items are distributed
- Reasoning: This makes tries to make sure an item is seen before user subscription expires.

**Example with urgent new item:**
- U1 has seen [A,B,C], needs [D,E]
- Item F appears with 2 days subscription left
- Item D has 5 days left, Item E has 3 days left
- **Result:** F gets priority over D but not E
- U1's next batch: [E, F] then [D]
- Reasoning: There is still 5 days for D to be seen.

### Algorithm Implementation Logic

### 1. Global Item State Tracking

```javascript
// Track which items have been seen "at least once" by ANY user
const globalItemState = {
  "item_A": { seen_at_least_once: true, first_seen_by: "user_1" },
  "item_B": { seen_at_least_once: true, first_seen_by: "user_2" },
  "item_C": { seen_at_least_once: false, first_seen_by: null },
  // ...
}
```

### 2. Batch Distribution Algorithm

```javascript
function distributeBatchesToUsers(users, availableItems, batchSize) {
  // Step 1: Identify items never seen by anyone
  const neverSeenItems = availableItems.filter(item => 
    !globalItemState[item.id].seen_at_least_once
  );
  
  // Step 2: For each user, get their unseen items
  const userUnseenItems = {};
  users.forEach(user => {
    userUnseenItems[user.id] = availableItems.filter(item => 
      !userHasSeenItem(user.id, item.id)
    );
  });
  
  // Step 3: Distribute batches with minimum intersection
  const userBatches = {};
  
  if (neverSeenItems.length > 0) {
    // Priority: Distribute never-seen items first
    userBatches = distributeNeverSeenItems(users, neverSeenItems, batchSize);
  } else {
    // All items seen at least once, focus on completing individual user journeys
    userBatches = distributeWithMinimumIntersection(users, userUnseenItems, batchSize);
  }
  
  return userBatches;
}
```

### 2. Minimum Intersection Distribution

```javascript
function distributeWithMinimumIntersection(users, userUnseenItems, batchSize) {
  const batches = {};
  const usedItemsInCurrentBatch = new Set();
  
  // First pass: distribute items ensuring minimum intersection
  users.forEach(user => {
    const availableForUser = userUnseenItems[user.id].filter(item => 
      !usedItemsInCurrentBatch.has(item.id)
    );
    
    // Take items with priority for subscription urgency
    let userBatch = availableForUser
      .sort((a, b) => a.subscription_days_left - b.subscription_days_left)
      .slice(0, Math.min(batchSize, availableForUser.length));
    
    // If we couldn't fill the batch due to intersection constraints,
    // fill remaining slots with any available items for this user
    if (userBatch.length < batchSize) {
      const remainingSlots = batchSize - userBatch.length;
      const additionalItems = userUnseenItems[user.id]
        .filter(item => !userBatch.find(bItem => bItem.id === item.id))
        .slice(0, remainingSlots);
      
      userBatch = [...userBatch, ...additionalItems];
    }
    
    batches[user.id] = userBatch;
    
    // Mark these items as used in this batch round to minimize intersection
    userBatch.forEach(item => usedItemsInCurrentBatch.add(item.id));
  });
  
  return batches;
}
```

### 4. "All Caught Up" Detection

```javascript
function checkIfUsersCaughtUp(users, availableItems) {
  const allCaughtUp = users.every(user => {
    const unseenCount = availableItems.filter(item => 
      !userHasSeenItem(user.id, item.id)
    ).length;
    return unseenCount === 0;
  });
  
  const noNewItemsSinceLastCheck = !hasNewItemsSince(getLastGlobalCheck());
  
  return allCaughtUp && noNewItemsSinceLastCheck;
}
```

### Real-World Scaling Example

**With 50,000 items and 1,000 users:**

1. **Phase 1:** Focus on ensuring each of 50,000 items is seen at least once
2. **Batching:** With 10 items per batch, need 5,000 total batch views to achieve "at least once" coverage
3. **Distribution:** 1,000 users × 5 batches each = 5,000 batch views ✓
4. **Phase 2:** Continue until each user has seen all 50,000 items (500 more batches per user), at the end, we want each item seen atleast once (Global fairness) and each user seeing all items (individual completeness)


## Database Schema
-Note that we have some tables already and this is just as guide, we will still optimise for the behavior i like through iteration, this document just gives you like 65% of the idea, we will still iterate where i dont like as we build.
### Core Tables

**users**
```sql
- user_id (Primary Key)
- username
- subscription_end_date
- created_at
- updated_at
```

**items**
```sql
- item_id (Primary Key)
- seller_id (Foreign Key to users)
- title
- description
- is_active (Boolean - on/off switch)
- created_at
- updated_at
```

**user_item_views**
```sql
- view_id (Primary Key)
- user_id (Foreign Key)
- item_id (Foreign Key)
- view_round (Integer - which cycle of viewing)
- viewed_at (Timestamp)
```

**user_feed_state**
```sql
- user_id (Primary Key)
- current_round (Integer - current viewing cycle)
- last_feed_request (Timestamp)
- is_caught_up (Boolean)
- updated_at
```

## Cache Structure

### Redis Cache Keys

**Eligible Items Cache:**
```
Key: "eligible_items"
Value: JSON array of item objects
TTL: 5 minutes
Structure: [
  {
    "item_id": 123,
    "seller_id": 456,
    "title": "Item Title",
    "created_at": "2025-07-30T10:00:00Z"
  }
]
```

**Cache Invalidation Flags:**
```
Key: "cache_dirty"
Value: Boolean
TTL: No expiration (manual control)
```

## Dynamic Sequence Management & Guest User Handling

### Pre-computed User Sequences Strategy

Instead of computing feeds on-demand, we pre-compute optimized sequences for all users that minimize intersection while ensuring fair distribution. (This is not final, it could still change as we iterate, no feature is fully final yet, we can only know as we iterate.)

**Sequence Generation Logic:**
```javascript
// Generate sequences for all users ensuring minimum intersection
function generateUserSequences(users, items) {
  const sequences = {};
  const itemPositionTracker = {}; // Track which positions each item occupies
  
  // Initialize position tracker
  items.forEach(item => {
    itemPositionTracker[item.id] = new Set();
  });
  
  users.forEach((user, userIndex) => {
    const userSequence = [];
    const shuffledItems = [...items];
    
    // Create user-specific shuffle based on user ID to ensure consistency
    shuffleArrayWithSeed(shuffledItems, user.id);
    
    shuffledItems.forEach((item, position) => {
      // Try to place item in position where it hasn't been placed for other users
      let finalPosition = position;
      
      // If this position is overused, find next available position
      while (itemPositionTracker[item.id].has(finalPosition) && 
             itemPositionTracker[item.id].size < users.length) {
        finalPosition = (finalPosition + 1) % items.length;
      }
      
      userSequence[finalPosition] = item;
      itemPositionTracker[item.id].add(finalPosition);
    });
    
    sequences[user.id] = userSequence.filter(Boolean); // Remove empty slots
  });
  
  return sequences;
}
```

### Handling Inactive Users & Dynamic Rebalancing

**Problem:** User U1 gets sequence but never logs in, their "reserved" items remain unseen.

**Solution:** Adaptive rebalancing with sequence preservation

```javascript
// Background job that runs every 10 minutes
async function rebalanceInactiveUserSequences() {
  const activeUsers = await getActiveUsers(24); // Users active in last 24 hours
  const inactiveUsers = await getInactiveUsers(24);
  
  // Check if rebalancing is needed
  const unseenItemsFromInactive = await getUnseenItemsFromInactiveUsers(inactiveUsers);
  
  if (unseenItemsFromInactive.length > 0) {
    // Redistribute unseen items among active users
    await redistributeItemsToActiveUsers(activeUsers, unseenItemsFromInactive);
  }
}

async function redistributeItemsToActiveUsers(activeUsers, unseenItems) {
  // Get current global state to understand which items still need "at least once" exposure
  const globallyUnseenItems = await getGloballyUnseenItems();
  const priorityItems = unseenItems.filter(item => globallyUnseenItems.includes(item.id));
  const regularItems = unseenItems.filter(item => !globallyUnseenItems.includes(item.id));
  
  // Distribute priority items first (not yet seen by anyone)
  const priorityDistribution = distributeItemsWithMinimumIntersection(activeUsers, priorityItems);
  
  // Then distribute regular items (for individual user completion)
  const regularDistribution = distributeItemsWithMinimumIntersection(activeUsers, regularItems);
  
  // Merge distributions and update user sequences
  for (const user of activeUsers) {
    const userPriorityItems = priorityDistribution[user.id] || [];
    const userRegularItems = regularDistribution[user.id] || [];
    const allUserItems = [...userPriorityItems, ...userRegularItems];
    
    if (allUserItems.length > 0) {
      await insertItemsIntoUserSequence(user.id, allUserItems);
    }
  }
}

function distributeItemsWithMinimumIntersection(users, items) {
  const distribution = {};
  const usedItems = new Set();
  
  users.forEach((user, index) => {
    const availableItems = items.filter(item => !usedItems.has(item.id));
    const userShare = Math.ceil(availableItems.length / (users.length - index));
    
    distribution[user.id] = availableItems.slice(0, userShare);
    distribution[user.id].forEach(item => usedItems.add(item.id));
  });
  
  return distribution;
}
```

### Returning Inactive Users Strategy

**Option 1: Preserve Original Sequence (Recommended)**
```javascript
async function handleReturningUser(userId) {
  const userSequence = await getUserSequence(userId);
  const userProgress = await getUserProgress(userId);
  
  // Keep original sequence intact
  // Background job will handle global rebalancing
  // User continues from where they left off
  
  return {
    sequence: userSequence,
    startIndex: userProgress.last_seen_index,
    requiresRebalancing: false
  };
}
```

**Option 2: Smart Rebalancing (Advanced)**
```javascript
async function handleReturningUserWithRebalancing(userId) {
  const originalSequence = await getUserSequence(userId);
  const userProgress = await getUserProgress(userId);
  const globalState = await getGlobalItemDistributionState();
  
  // Check if user's sequence needs updating
  const rebalanceNeeded = checkIfRebalanceNeeded(originalSequence, globalState);
  
  if (rebalanceNeeded) {
    // Schedule background rebalancing
    scheduleUserSequenceUpdate(userId);
    
    // Return current sequence while rebalancing happens in background
    return {
      sequence: originalSequence,
      startIndex: userProgress.last_seen_index,
      requiresRebalancing: true
    };
  }
  
  return {
    sequence: originalSequence,
    startIndex: userProgress.last_seen_index,
    requiresRebalancing: false
  };
}
```


### Guest User Management System

**Guest ID Generation & Storage:**
```javascript
// Client-side (React Native)
import AsyncStorage from '@react-native-async-storage/async-storage';

async function getOrCreateGuestId() {
  try {
    let guestId = await AsyncStorage.getItem('guest_user_id');
    
    if (!guestId) {
      // Generate new guest ID
      guestId = `guest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      await AsyncStorage.setItem('guest_user_id', guestId);
    }
    
    // Validate guest ID with backend
    const isValid = await validateGuestIdWithBackend(guestId);
    
    if (!isValid) {
      // Guest ID expired or doesn't exist, create new one
      guestId = `guest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      await AsyncStorage.setItem('guest_user_id', guestId);
      await registerGuestIdWithBackend(guestId);
    }
    
    return guestId;
  } catch (error) {
    console.error('Guest ID management error:', error);
    // Fallback: generate session-only guest ID
    return `guest_session_${Date.now()}`;
  }
}

async function validateGuestIdWithBackend(guestId) {
  try {
    const response = await fetch('/api/guest/validate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ guest_id: guestId })
    });
    
    const result = await response.json();
    return result.valid;
  } catch (error) {
    return false;
  }
}
```

**Backend Guest Management:**
```javascript
// Guest users table
/*
guest_users:
- guest_id (Primary Key)
- created_at
- last_active_at  
- expires_at (created_at + 20 days)
- is_active (Boolean)
*/

async function registerGuestUser(guestId) {
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + 20); // 20 days from now
  
  await database.execute(`
    INSERT INTO guest_users (guest_id, created_at, last_active_at, expires_at, is_active)
    VALUES (?, NOW(), NOW(), ?, true)
    ON DUPLICATE KEY UPDATE 
      last_active_at = NOW(),
      expires_at = DATE_ADD(NOW(), INTERVAL 20 DAY),
      is_active = true
  `, [guestId, expiresAt]);
  
  // Assign feed sequence to guest user
  await assignSequenceToNewUser(guestId);
}

async function validateGuestId(guestId) {
  const guest = await database.queryOne(`
    SELECT guest_id, expires_at, is_active
    FROM guest_users 
    WHERE guest_id = ? AND is_active = true
  `, [guestId]);
  
  if (!guest) {
    return { valid: false, reason: 'not_found' };
  }
  
  if (new Date() > new Date(guest.expires_at)) {
    // Mark as expired
    await database.execute(`
      UPDATE guest_users SET is_active = false WHERE guest_id = ?
    `, [guestId]);
    
    return { valid: false, reason: 'expired' };
  }
  
  // Update last active
  await database.execute(`
    UPDATE guest_users 
    SET last_active_at = NOW(),
        expires_at = DATE_ADD(NOW(), INTERVAL 20 DAY)
    WHERE guest_id = ?
  `, [guestId]);
  
  return { valid: true };
}
```

**Guest Cleanup Job:**
```javascript
// Daily cleanup job (runs at 12 AM)
async function cleanupExpiredGuests() {
  const expiredGuests = await database.query(`
    SELECT guest_id 
    FROM guest_users 
    WHERE expires_at < NOW() OR 
          (is_active = true AND DATEDIFF(NOW(), last_active_at) > 20)
  `);
  
  for (const guest of expiredGuests) {
    await cleanupGuestData(guest.guest_id);
  }
  
  // Mark guests as inactive
  await database.execute(`
    UPDATE guest_users 
    SET is_active = false 
    WHERE expires_at < NOW() OR 
          (is_active = true AND DATEDIFF(NOW(), last_active_at) > 20)
  `);
  
  console.log(`Cleaned up ${expiredGuests.length} expired guest accounts`);
}

async function cleanupGuestData(guestId) {
  // Remove guest's view history
  await database.execute(`
    DELETE FROM user_item_views WHERE user_id = ?
  `, [guestId]);
  
  // Remove guest's feed state
  await database.execute(`
    DELETE FROM user_feed_state WHERE user_id = ?
  `, [guestId]);
  
  // Remove cached sequences
  await cache.delete(`user_sequence_${guestId}`);
}
```

**Integrating Guest Views into Global Item State:**
```javascript
async function trackItemView(userId, itemId, isGuest = false) {
  // Record view for user (guest or registered)
  await database.execute(`
    INSERT INTO user_item_views (user_id, item_id, view_round, viewed_at, is_guest_view)
    VALUES (?, ?, ?, NOW(), ?)
  `, [userId, itemId, await getUserCurrentRound(userId), isGuest]);
  
  // Update global item state
  await database.execute(`
    INSERT INTO global_item_views (item_id, total_views, unique_viewers, first_seen_at)
    VALUES (?, 1, 1, NOW())
    ON DUPLICATE KEY UPDATE 
      total_views = total_views + 1,
      unique_viewers = unique_viewers + 1
  `, [itemId]);
  
  // Mark item as "seen at least once" globally
  await updateGlobalItemState(itemId, true);
}
```

This comprehensive system handles:
- **Dynamic rebalancing** when users are inactive
- **Guest user lifecycle** management with automatic cleanup
- **Sequence preservation** vs **adaptive rebalancing** options
- **Cross-platform data persistence** using AsyncStorage

### 1. Background Cache Update Job

```javascript
// Runs every 5 minutes
async function updateEligibleItemsCache() {
  try {
    // Get all items where seller has active subscription
    const eligibleItems = await database.query(`
      SELECT i.item_id, i.seller_id, i.title, i.description, i.created_at
      FROM items i
      JOIN users u ON i.seller_id = u.user_id
      WHERE i.is_active = true 
      AND u.subscription_end_date > NOW()
      ORDER BY i.created_at DESC
    `);
    
    // Store in cache
    await cache.set("eligible_items", JSON.stringify(eligibleItems), 300); // 5 minutes TTL
    await cache.delete("cache_dirty"); // Mark cache as clean
    
    console.log(`Cache updated with ${eligibleItems.length} eligible items`);
  } catch (error) {
    console.error("Cache update failed:", error);
  }
}
```

### 2. Get User Feed Function

```javascript
async function getUserFeed(userId, page = 1, itemsPerPage = 10) {
  try {
    // Get cached eligible items
    let eligibleItems = await cache.get("eligible_items");
    
    if (!eligibleItems) {
      // Fallback: compute on demand if cache is empty
      await updateEligibleItemsCache();
      eligibleItems = await cache.get("eligible_items");
    }
    
    eligibleItems = JSON.parse(eligibleItems);
    
    // Get user's current viewing state
    let userState = await getUserFeedState(userId);
    
    // Get items user hasn't seen in current round
    const unseenItems = await getUnseenItemsForUser(userId, userState.current_round, eligibleItems);
    
    // If no unseen items, start new round
    if (unseenItems.length === 0) {
      userState = await startNewRoundForUser(userId, eligibleItems);
      if (userState.is_caught_up) {
        return {
          items: [],
          has_more: false,
          message: "You're all caught up! Check back later for new items."
        };
      }
      unseenItems = await getUnseenItemsForUser(userId, userState.current_round, eligibleItems);
    }
    
    // Apply fair distribution algorithm
    const distributedItems = applyFairDistribution(unseenItems, userId);
    
    // Paginate results
    const startIndex = (page - 1) * itemsPerPage;
    const paginatedItems = distributedItems.slice(startIndex, startIndex + itemsPerPage);
    
    // Mark items as viewed (We may need to only need to mark item as viewed once it comes in view.)
    if (paginatedItems.length > 0) {
      await markItemsAsViewed(userId, paginatedItems.map(item => item.item_id), userState.current_round);
    }
    
    return {
      items: paginatedItems,
      has_more: startIndex + itemsPerPage < distributedItems.length,
      current_round: userState.current_round,
      total_eligible_items: eligibleItems.length
    };
    
  } catch (error) {
    console.error("Get user feed error:", error);
    throw error;
  }
}
```


### 3. Fair Distribution Algorithm

```javascript
function applyFairDistribution(items, userId) {
  // Create user-specific shuffle seed based on user ID and current time
  const seed = userId + Math.floor(Date.now() / (1000 * 60 * 60)); // Changes every hour
  
  // Simple fair distribution: rotate starting point based on user ID
  const startingIndex = userId % items.length;
  
  // Reorder items so different users see different sequences
  const reorderedItems = [
    ...items.slice(startingIndex),
    ...items.slice(0, startingIndex)
  ];
  
  return reorderedItems;
}
```

### 4. View Tracking Functions

```javascript
async function getUnseenItemsForUser(userId, currentRound, eligibleItems) {
  const viewedItemIds = await database.query(`
    SELECT item_id 
    FROM user_item_views 
    WHERE user_id = ? AND view_round = ?
  `, [userId, currentRound]);
  
  const viewedIds = new Set(viewedItemIds.map(row => row.item_id));
  
  return eligibleItems.filter(item => !viewedIds.has(item.item_id));
}

async function markItemsAsViewed(userId, itemIds, viewRound) {
  const values = itemIds.map(itemId => [userId, itemId, viewRound, new Date()]);
  
  await database.executeBatch(`
    INSERT IGNORE INTO user_item_views (user_id, item_id, view_round, viewed_at)
    VALUES (?, ?, ?, ?)
  `, values);
}
```

## Real-time Item Toggle Handling

### When Seller Toggles Item On/Off

```javascript
async function toggleItemStatus(itemId, sellerId, isActive) {
  try {
    // Update database
    await database.execute(`
      UPDATE items 
      SET is_active = ?, updated_at = NOW()
      WHERE item_id = ? AND seller_id = ?
    `, [isActive, itemId, sellerId]);
    
    // Mark cache as dirty for immediate refresh
    await cache.set("cache_dirty", "true");
    
    // Optional: Force immediate cache update for critical changes
    if (isActive) {
      // Item turned on - refresh cache immediately
      await updateEligibleItemsCache();
    }
    
    return { success: true };
  } catch (error) {
    console.error("Toggle item status error:", error);
    throw error;
  }
}
```

## API Endpoints Structure

### Get Feed Endpoint
```
GET /api/feed?page=1&limit=10

Response:
{
  "items": [...],
  "has_more": true,
  "current_round": 2,
  "message": null
}
```

### Toggle Item Endpoint
```
POST /api/items/{item_id}/toggle

Body: { "is_active": true }

Response:
{
  "success": true,
  "item_id": 123,
  "is_active": true
}
```

## Performance Monitoring

### Key Metrics to Track
- Cache hit rate (should be >95%)
- Average feed response time (<200ms)
- Background job execution time
- Items distribution fairness (each user sees similar variety)

### Error Handling
- Cache failures: Fall back to direct database queries
- Database failures: Return cached data with warning
- Background job failures: Log and retry with exponential backoff

## Deployment Checklist

1. Set up Redis cache server
2. Create database indexes:
   ```sql
   CREATE INDEX idx_items_active_seller ON items(is_active, seller_id);
   CREATE INDEX idx_users_subscription ON users(subscription_end_date);
   CREATE INDEX idx_user_views_round ON user_item_views(user_id, view_round);
   ```
3. Schedule background job (cron job every 5 minutes)
4. Set up cache monitoring and alerts
5. Test with multiple users to verify fair distribution

This implementation provides a solid foundation that can handle thousands of users while maintaining fair item distribution and real-time responsiveness to item status changes.