import axios from 'axios';

// This URL will be automatically updated by the deployment script
// DO NOT CHANGE THE FORMAT of this line as it's used by the script
const API_URL = 'https://0q229lu1qk.execute-api.us-east-1.amazonaws.com/dev';

// Check if the AWS API is properly configured
const isConfigured = API_URL && API_URL.length > 10 && !API_URL.includes('your-api-gateway-url');
console.log('API URL:', API_URL);
console.log('API is configured:', isConfigured);

// Helper function to construct the proper endpoint URL
const getEndpointUrl = (path) => {
  // Ensure there's no double slashes between the base URL and path
  // Remove trailing slash from API_URL if present
  const baseUrl = API_URL.endsWith('/') ? API_URL.slice(0, -1) : API_URL;
  // Remove leading slash from path if present
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  return `${baseUrl}/${cleanPath}`;
};

// API function to check if a phone number exists in the backend
export const checkPhoneNumber = async (phoneNumber) => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Using mock data.');
    return false; // Default to user not exists
  }

  const endpoint = getEndpointUrl('check-phone');
  console.log(`Calling AWS API: ${endpoint} with phone: ${phoneNumber}`);

  try {
    // Make a POST request to the backend
    const response = await axios.post(endpoint, {
      phoneNumber: phoneNumber, // Send the phone number in the request body
    }, {
      headers: {
        'Content-Type': 'application/json',
      },
      // Optional: Set a timeout for the request (e.g., 10 seconds)
      timeout: 10000,
    });

    console.log('AWS API Response Status:', response.status);
    console.log('AWS API Response Data:', response.data);

    // Expecting a boolean `userExists` in the response data
    if (typeof response.data.userExists === 'boolean') {
      return response.data.userExists;
    } else {
      // Handle unexpected response format
      console.error('Unexpected response format:', response.data);
      throw new Error('Invalid response format from AWS server.');
    }

  } catch (error) {
    console.error('AWS API Error checking phone number:', error);

    // Check if the error has response data (e.g., for 4xx or 5xx errors)
    if (error.response) {
      console.error('AWS API Error Response Data:', error.response.data);
      console.error('AWS API Error Response Status:', error.response.status);
      console.error('AWS API Error Response Headers:', error.response.headers);
      // You could throw a more specific error based on status code here
      throw new Error(`AWS server error: ${error.response.status} - ${error.response.data?.message || 'Unknown error'}`);
    } else if (error.request) {
      // The request was made but no response was received (e.g., network error, timeout)
      console.error('AWS API Error Request:', error.request);
      throw new Error('Network error or no response from AWS server.');
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('AWS API Setup Error Message:', error.message);
      throw new Error('Error setting up AWS API request: ' + error.message);
    }
  }
};

// API function to check for pending verification code
export const checkPendingCode = async (phoneNumber) => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Using mock data.');
    // Return mock data for development - simulate no pending code
    return {
      success: true,
      pending: false,
      message: "Mock: No pending verification code found"
    };
  }

  const endpoint = getEndpointUrl('check-pending-code');
  console.log(`Calling AWS API: ${endpoint} with phone: ${phoneNumber}`);

  try {
    const response = await axios.post(endpoint, {
      phoneNumber: phoneNumber,
    }, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    });

    console.log('AWS API Response Status:', response.status);
    console.log('AWS API Response Data:', response.data);

    return {
      success: response.data.success || false,
      pending: response.data.pending || false,
      remainingTime: response.data.remainingTime || 0,
      message: response.data.message
    };
  } catch (error) {
    console.error('AWS API Error checking pending code:', error);
    return {
      success: false,
      pending: false,
      error: handleAxiosError(error, 'Error checking pending code')
    };
  }
};

// API function to send verification code to a phone number
export const sendVerificationCode = async (phoneNumber) => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Using mock data.');
    // Return mock data for development
    return {
      success: true,
      expiresAt: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes from now
      isExisting: false,
      message: "Mock verification code sent"
    };
  }

  const endpoint = getEndpointUrl('send-code');
  console.log(`Calling AWS API: ${endpoint} with phone: ${phoneNumber}`);

  try {
    const response = await axios.post(endpoint, {
      phoneNumber: phoneNumber,
    }, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    });

    console.log('AWS API Response Status:', response.status);
    console.log('AWS API Response Data:', response.data);

    return {
      success: true,
      expiresAt: response.data.expiresAt ? new Date(response.data.expiresAt) : null,
      isExisting: response.data.isExisting || false,
      message: response.data.message
    };
  } catch (error) {
    console.error('AWS API Error sending verification code:', error);
    return {
      success: false,
      error: handleAxiosError(error, 'Error sending verification code')
    };
  }
};

// API function to verify a code
export const verifyCode = async (phoneNumber, code) => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Using mock data.');
    // For testing, always consider "123456" as valid
    const valid = code === "123456";
    return {
      valid,
      sessionToken: valid ? `mock-session-token-${Date.now()}` : null,
      user: valid ? { id: 'mock-user-id', phoneNumber, name: 'Mock User' } : null,
      reason: valid ? null : 'invalid_code'
    };
  }

  const endpoint = getEndpointUrl('verify-code');
  console.log(`Calling AWS API: ${endpoint} with phone: ${phoneNumber} and code: ${code}`);

  try {
    const response = await axios.post(endpoint, {
      phoneNumber: phoneNumber,
      code: code,
    }, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    });

    console.log('AWS API Response Status:', response.status);
    console.log('AWS API Response Data:', response.data);

    return {
      valid: response.data.success || false,
      sessionToken: response.data.sessionToken,
      user: response.data.user,
      reason: response.data.message
    };
  } catch (error) {
    console.error('AWS API Error verifying code:', error);
    return { 
      valid: false, 
      reason: 'server_error',
      error: handleAxiosError(error, 'Error verifying code')
    };
  }
};

// API function to register a new user
export const registerUser = async (userData) => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Using mock data.');
    // Return mock data for development
    return {
      success: true,
      userId: `mock-user-id-${Date.now()}`,
      sessionToken: `mock-session-token-${Date.now()}`,
      user: {
        id: `mock-user-id-${Date.now()}`,
        ...userData,
        created_at: new Date().toISOString()
      }
    };
  }

  const endpoint = getEndpointUrl('register-user');
  console.log(`[aws-api] Attempting to register user. Endpoint: ${endpoint}`); 
  console.log(`[aws-api] Sending userData:`, JSON.stringify(userData, null, 2)); 

  try {
    const response = await axios.post(endpoint, userData, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    });

    console.log('AWS API Response Status:', response.status);
    console.log('AWS API Response Data:', response.data);

    return {
      success: true,
      userId: response.data.userId,
      sessionToken: response.data.sessionToken,
      user: response.data.user
    };
  } catch (error) {
    // Added detailed logging
    console.error('[aws-api] Axios error during registration:', error); 
    if (error.response) {
       console.error('[aws-api] Axios error response data:', error.response.data);
       console.error('[aws-api] Axios error response status:', error.response.status);
    } else if (error.request) {
       console.error('[aws-api] Axios error request:', error.request);
    } else {
       console.error('[aws-api] Axios error message:', error.message);
    }
    // Original error handling below
    console.error('AWS API Error registering user:', error);
    return { 
      success: false, 
      error: handleAxiosError(error, 'Error registering user')
    };
  }
};

/**
 * API function to validate a session token against a phone number
 * @param {string} sessionToken
 * @param {string} phoneNumber
 * @returns {Object} - { isValid, error }
 */
export const checkSession = async (sessionToken, phoneNumber) => {
  if (!isConfigured) {
    console.warn('AWS API not configured; defaulting to invalid session.');
    return { isValid: false };
  }
  const endpoint = getEndpointUrl('check-session');
  console.log(`[DEBUG] Checking session at endpoint: ${endpoint}`);
  console.log(`[DEBUG] API_URL used: ${API_URL}`);
  console.log(`[DEBUG] Session token: ${sessionToken?.substring(0, 10)}...`);
  console.log(`[DEBUG] Phone number: ${phoneNumber}`);
  
  try {
    console.log(`[DEBUG] Making POST request with data:`, { sessionToken, phoneNumber });
    const response = await axios.post(endpoint, { sessionToken, phoneNumber }, {
      headers: { 
        'Content-Type': 'application/json' 
      },
      timeout: 10000,
    });
    console.log(`[DEBUG] Session check response:`, response.data);
    return { isValid: response.data.isValid };
  } catch (error) {
    console.error('[DEBUG] AWS API Error checking session:', error);
    
    if (error.response) {
      console.error('[DEBUG] Error status:', error.response.status);
      console.error('[DEBUG] Error headers:', error.response.headers);
      console.error('[DEBUG] Error data:', error.response.data);
    } else if (error.request) {
      console.error('[DEBUG] No response received, request details:', error.request);
    } else {
      console.error('[DEBUG] Request setup error:', error.message);
    }
    
    return { isValid: false, error: handleAxiosError(error, 'Error checking session') };
  }
};

// API function to get user settings
export const getUserSettings = async (sessionToken) => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Using mock data.');
    // Return mock data for development
    return {
      success: true,
      userSettings: {
        userId: 'mock-user-id',
        name: 'Mock User',
        phoneNumber: '+264812345678',
        stallName: 'Mock Stall',
        profilePicture: null
      }
    };
  }

  const endpoint = getEndpointUrl('get-user-settings');
  console.log(`Calling AWS API: ${endpoint} with session token`);

  try {
    const response = await axios.get(endpoint, {
      headers: {
        'Content-Type': 'application/json',
        'X-Session-Token': sessionToken
      },
      timeout: 10000,
    });

    console.log('AWS API Response Status:', response.status);
    console.log('AWS API Response Data:', response.data);

    return {
      success: true,
      userSettings: response.data.userSettings
    };
  } catch (error) {
    console.error('AWS API Error getting user settings:', error);
    return {
      success: false,
      error: handleAxiosError(error, 'Error getting user settings')
    };
  }
};

// API function to update user settings
export const updateUserSettings = async (userId, userData) => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Using mock data.');
    // Return mock data for development
    return {
      success: true,
      user: {
        ...userData,
        userId: userId || 'mock-user-id',
        phoneNumber: '+264812345678',
        profilePicture: userData.profilePicture ? 'https://example.com/mock-profile-pic.jpg' : null
      }
    };
  }

  const endpoint = getEndpointUrl('update-user-settings');
  console.log(`Calling AWS API: ${endpoint} with userId and user data:`, userData);

  // Prepare the request body with userId and user data
  const requestBody = {
    userId: userId,
    ...userData
  };

  try {
    const response = await axios.post(endpoint, requestBody, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 30000, // Longer timeout for image uploads
    });

    console.log('AWS API Response Status:', response.status);
    console.log('AWS API Response Data:', response.data);

    return {
      success: response.data.success || false,
      user: response.data.user,
      message: response.data.message
    };
  } catch (error) {
    console.error('AWS API Error updating user settings:', error);
    return {
      success: false,
      error: handleAxiosError(error, 'Error updating user settings')
    };
  }
};

// API function to get stall data (user items + user info)
export const getStallData = async (sessionToken) => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Using mock data.');
    // Return mock stall data for development
    return {
      success: true,
      stallData: {
        userInfo: {
          userId: 'mock-user-id',
          name: 'Mock User',
          phoneNumber: '+264812345678',
          stallName: 'Mock Stall',
          days: 25,
          profilePicture: null
        },
        items: [
          {
            id: 'item-1',
            userId: 'mock-user-id',
            name: 'Mock Item 1',
            description: 'This is a mock item for testing',
            price: 50,
            category: 'Food',
            images: ['https://via.placeholder.com/300x200'],
            isVisible: true,
            createdAt: Date.now() - 86400000 // 1 day ago
          },
          {
            id: 'item-2',
            userId: 'mock-user-id',
            name: 'Mock Item 2',
            description: 'Another mock item for testing',
            price: 75,
            category: 'Craft',
            images: ['https://via.placeholder.com/300x200'],
            isVisible: true,
            createdAt: Date.now() - 172800000 // 2 days ago
          }
        ]
      }
    };
  }

  const endpoint = getEndpointUrl('get-stall-data');
  console.log(`Calling AWS API: ${endpoint} with session token`);

  try {
    const response = await axios.get(endpoint, {
      headers: {
        'Content-Type': 'application/json',
        'X-Session-Token': sessionToken
      },
      timeout: 15000, // 15 second timeout for potentially large data
    });

    console.log('AWS API Response Status:', response.status);
    console.log('AWS API Response Data:', response.data);

    return {
      success: true,
      stallData: response.data.stallData
    };
  } catch (error) {
    console.error('AWS API Error getting stall data:', error);
    return {
      success: false,
      error: handleAxiosError(error, 'Error getting stall data')
    };
  }
};

// Helper function to handle Axios errors consistently
const handleAxiosError = (error, defaultMessage) => {
  if (error.response) {
    console.error('AWS API Error Response Data:', error.response.data);
    console.error('AWS API Error Response Status:', error.response.status);
    return new Error(`AWS server error: ${error.response.status} - ${error.response.data?.message || defaultMessage}`);
  } else if (error.request) {
    console.error('AWS API Error Request:', error.request);
    return new Error('Network error or no response from AWS server.');
  } else {
    console.error('AWS API Error Message:', error.message);
    return new Error('Error setting up AWS API request: ' + error.message);
  }
};

// API function to add an item
export const addItem = async (itemData) => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Using mock data.');
    // Return mock success for development
    return {
      success: true,
      item: {
        itemId: `mock-item-id-${Date.now()}`,
        ...itemData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };
  }

  const endpoint = getEndpointUrl('add-item');
  console.log(`Calling AWS API: ${endpoint} to add item`);
  console.log('Item data:', JSON.stringify(itemData, null, 2));

  try {
    const response = await axios.post(endpoint, itemData, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 seconds timeout for image uploads
    });

    console.log('AWS API Response Status:', response.status);
    console.log('AWS API Response Data:', response.data);

    return {
      success: response.data.success || false,
      item: response.data.item,
      message: response.data.message
    };
  } catch (error) {
    console.error('AWS API Error adding item:', error);
    return {
      success: false,
      error: handleAxiosError(error, 'Error adding item')
    };
  }
};

// API function to remove user profile picture
export const removeUserProfilePicture = async (userId) => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Using mock data.');
    // Return mock success for development
    return {
      success: true,
      message: 'Profile picture removed successfully (mock)'
    };
  }

  const endpoint = getEndpointUrl('remove-profile-picture');
  console.log(`Calling AWS API: ${endpoint} to remove profile picture for user:`, userId);

  // Prepare the request body with userId
  const requestBody = {
    userId: userId
  };

  try {
    const response = await axios.post(endpoint, requestBody, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 15000,
    });

    console.log('AWS API Response Status:', response.status);
    console.log('AWS API Response Data:', response.data);

    return {
      success: response.data.success || false,
      message: response.data.message
    };
  } catch (error) {
    console.error('AWS API Error removing profile picture:', error);
    return {
      success: false,
      error: handleAxiosError(error, 'Error removing profile picture')
    };
  }
};

// API function to update item
export const updateItem = async (data) => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Using mock data.');
    // Return mock success for development
    return {
      success: true,
      data: { itemId: data.itemId, message: 'Item updated successfully (mock)' }
    };
  }

  const endpoint = getEndpointUrl('update-item');
  console.log(`Calling AWS API: ${endpoint} to update item:`, data.itemId);

  try {
    const response = await axios.post(endpoint, data, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 30000, // Longer timeout for image uploads
    });

    console.log('AWS API Response Status:', response.status);
    console.log('AWS API Response Data:', response.data);

    return {
      success: response.data.success || false,
      data: response.data.data,
      message: response.data.message
    };
  } catch (error) {
    console.error('AWS API Error updating item:', error);
    return {
      success: false,
      error: handleAxiosError(error, 'Error updating item')
    };
  }
};

// API function to recycle verification code during logout
export const recycleVerificationCode = async (phoneNumber) => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Skipping recycle verification code.');
    return { success: true, message: 'API not configured' };
  }

  const endpoint = getEndpointUrl('recycle-verification-code');
  console.log(`Calling AWS API: ${endpoint} with phone: ${phoneNumber}`);

  try {
    const response = await axios.post(endpoint, {
      phoneNumber: phoneNumber,
    }, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    });

    console.log('AWS API Response Status:', response.status);
    console.log('AWS API Response Data:', response.data);

    return {
      success: response.data.success || true,
      message: response.data.message || 'Verification code recycled'
    };
  } catch (error) {
    console.error('AWS API Error recycling verification code:', error);
    // Always return success to ensure logout works even if recycling fails
    return {
      success: true,
      message: 'Logout proceeding (recycle failed safely)'
    };
  }
};

// API function to get categories from backend
export const getCategories = async () => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Using default categories.');
    return {
      success: true,
      categories: ['Food', 'Craft', 'Agriculture'],
      message: 'Using default categories (API not configured)'
    };
  }

  const endpoint = getEndpointUrl('get-categories');
  console.log(`Calling AWS API: ${endpoint}`);

  try {
    const response = await axios.get(endpoint, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    });

    console.log('Get Categories Response Status:', response.status);
    console.log('Get Categories Response Data:', response.data);

    if (response.data.success && response.data.categories) {
      return {
        success: true,
        categories: response.data.categories,
        message: response.data.message || 'Categories retrieved successfully'
      };
    } else {
      console.warn('Categories API returned unsuccessful response:', response.data);
      return {
        success: false,
        categories: null,
        error: response.data.error || 'Failed to retrieve categories'
      };
    }
  } catch (error) {
    console.error('AWS API Error getting categories:', error);

    // Return default categories on error to ensure app functionality
    return {
      success: true,
      categories: ['Food', 'Craft', 'Agriculture'],
      message: 'Using default categories due to API error',
      error: error.message
    };
  }
};

// API function to register a new guest user
export const guestRegister = async () => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Using mock data.');
    return {
      success: true,
      guestId: `mock_guest_${Date.now()}`,
      message: 'Mock guest registered'
    };
  }

  const endpoint = getEndpointUrl('guest-register');

  try {
    const response = await axios.post(endpoint, {}, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 15000,
    });

    return {
      success: response.data.success || false,
      guestId: response.data.guestId,
      message: response.data.message
    };
  } catch (error) {
    return {
      success: false,
      error: handleAxiosError(error, 'Error registering guest user')
    };
  }
};

// API function to verify an existing guest user
export const guestVerify = async (guestId) => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Using mock data.');
    return {
      success: true,
      guestId: guestId || `mock_guest_${Date.now()}`,
      message: 'Mock guest verified'
    };
  }

  const endpoint = getEndpointUrl('guest-verify');

  try {
    const response = await axios.post(endpoint, {
      guestId: guestId
    }, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 15000,
    });

    return {
      success: response.data.success || false,
      guestId: response.data.guestId,
      message: response.data.message
    };
  } catch (error) {
    return {
      success: false,
      error: handleAxiosError(error, 'Error verifying guest user')
    };
  }
};

// Export as default object for easier imports
export default {
  isConfigured,
  checkPhoneNumber,
  checkPendingCode,
  sendVerificationCode,
  verifyCode,
  checkSession,
  registerUser,
  getUserSettings,
  updateUserSettings,
  getStallData,
  addItem,
  removeUserProfilePicture,
  updateItem,
  recycleVerificationCode,
  getCategories,
  guestRegister,
  guestVerify
};
