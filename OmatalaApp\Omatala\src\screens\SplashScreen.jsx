import React, { useEffect } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import OmatalaLogoSvg from '../../assets/OmatalaLogoSvg.js';
import { omatalaFontStyle } from '../utils/fonts';
import { getSessionToken, validateSession } from '../backend/userService';
import { saveUserId } from '../utils/userIdStorage';
import AsyncStorage from '@react-native-async-storage/async-storage';
import awsApi from '../services/aws-api';

const SplashScreen = () => {
  const navigation = useNavigation();

  useEffect(() => {
    // On mount, validate existing session or check guest ID
    const init = async () => {
      try {
        console.log('🔍 SplashScreen: Starting initialization...');

        // Step 1: Check for sessionToken
        const token = await getSessionToken();
        if (token) {
          console.log('🔍 SplashScreen: Session token found, validating...');

          // Validate the session token
          const { valid, userId } = await validateSession(token);
          if (valid) {
            console.log('✅ SplashScreen: Session valid, navigating to Feed');
            // Save userId before navigating to Feed
            if (userId) {
              await saveUserId(userId);
            }
            navigation.reset({ index: 0, routes: [{ name: 'Feed' }] });
            return;
          } else {
            console.log('❌ SplashScreen: Session invalid, continuing to guest check');
            // Session invalid, continue to guest check below
          }
        } else {
          console.log('🔍 SplashScreen: No session token found, checking for guest ID...');
        }

        // Step 2: No valid sessionToken, check for guestID
        const guestId = await AsyncStorage.getItem('guestID');
        if (guestId) {
          console.log('🔍 SplashScreen: Guest ID found, verifying with backend:', guestId.substring(0, 20) + '...');

          // Verify guest ID with backend
          const verifyResult = await awsApi.guestVerify(guestId);
          if (verifyResult.success) {
            console.log('✅ SplashScreen: Guest ID verified, navigating to GuestFeed');

            // Update AsyncStorage if backend returned a different ID
            if (verifyResult.guestId !== guestId) {
              console.log('🔄 SplashScreen: Updating guest ID in AsyncStorage');
              await AsyncStorage.setItem('guestID', verifyResult.guestId);
            }

            navigation.reset({ index: 0, routes: [{ name: 'GuestFeed' }] });
            return;
          } else {
            console.log('❌ SplashScreen: Guest ID verification failed, navigating to PhoneNumber');
          }
        } else {
          console.log('🔍 SplashScreen: No guest ID found');
        }

        // Step 3: No valid session or guest ID, navigate to PhoneNumber
        console.log('🚀 SplashScreen: No valid credentials found, navigating to PhoneNumber');
        navigation.reset({ index: 0, routes: [{ name: 'PhoneNumber' }] });

      } catch (error) {
        console.error('❌ SplashScreen: Error during initialization:', error);
        navigation.reset({ index: 0, routes: [{ name: 'PhoneNumber' }] });
      }
    };
    init();
  }, [navigation]);

  return (
    <View style={styles.container}>
      <View style={styles.logoContainer}>
        <OmatalaLogoSvg width={105} height={105} />
      </View>
      <View style={styles.bottomContainer}>
        <Text style={styles.onlineText}>Omatala</Text>
        <Text style={styles.onlineSubText}>online</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 100,
  },
  logoContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomContainer: {
    alignItems: 'center',
  },
  onlineText: {
    fontSize: 40,
    color: '#FF6A00',
    fontWeight: 'bold',
    fontFamily: 'Fredoka-Regular',
  },
  onlineSubText: {
    fontSize: 17, // 30% smaller than original 24px (24 * 0.7 = 16.8 ≈ 17)
    color: '#888888', // Grey color
    marginTop: -5,
  }
});

export default SplashScreen; 