terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    # Added archive provider for zipping Lambda code
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.2"
    }
  }
  required_version = ">= 1.2.0"
}

provider "aws" {
  region = var.aws_region
  # AWS credentials should be configured via AWS CLI (`aws configure`) or environment variables
}

# Get current AWS account ID - needed for Lambda permissions
data "aws_caller_identity" "current" {}







# Local variables
locals {
  project_name = "Omatala-online" # Keep your project name consistent
  environment  = var.environment
  tags = {
    Project     = local.project_name
    Environment = local.environment
  }
}

# --- DynamoDB Module ---
module "dynamo_db" {
  source = "./modules/dynamo_db"

  project_name = local.project_name
  environment  = local.environment
}



# --- SNS Topic for SMS Verification ---
resource "aws_sns_topic" "sms_verification" {
  name = "${local.project_name}-sms-verification-${local.environment}"

  # Optional: Add default SMS attributes if needed
  # sms_caller_id                  = "OmatalaApp" # Example Sender ID (check regional availability/regulations)
  # sms_delivery_status_iam_role_arn = aws_iam_role.sns_delivery_status_role.arn # If you want delivery logs
  # sms_delivery_status_success_sampling_rate = 100 # Log all success statuses

  tags = local.tags
}

# Using custom auth - Cognito module removed

# --- CloudWatch Log Group for API Gateway ---
resource "aws_cloudwatch_log_group" "api_gateway_logs" {
  name              = "${local.project_name}-api-gateway-logs-${local.environment}"
  retention_in_days = 7
  tags = local.tags
}

# --- API Gateway Module ---
module "api_gateway" {
  source = "./modules/api_gateway"

  project_name          = local.project_name
  environment           = local.environment
  aws_region            = var.aws_region
  cloudwatch_log_group_arn = aws_cloudwatch_log_group.api_gateway_logs.arn

  # Using custom auth - no Cognito references needed
}

# --- Lambda Module ---
module "lambda" {
  source = "./modules/lambda"

  project_name             = local.project_name
  environment              = local.environment
  region                   = var.aws_region
  account_id               = data.aws_caller_identity.current.account_id
  api_gateway_id           = module.api_gateway.api_gateway_id
  api_gateway_rest_api_id  = module.api_gateway.api_gateway_id
  api_root_resource_id     = module.api_gateway.root_resource_id
  api_gateway_root_id      = module.api_gateway.root_resource_id
  check_phone_resource_id  = module.api_gateway.check_phone_resource_id
  send_code_resource_id    = module.api_gateway.send_code_resource_id
  verify_code_resource_id  = module.api_gateway.verify_code_resource_id
  check_pending_code_resource_id = module.api_gateway.check_pending_code_resource_id
  register_user_resource_id = module.api_gateway.register_user_resource_id
  check_session_resource_id = module.api_gateway.check_session_resource_id
  get_stall_data_resource_id = module.api_gateway.get_stall_data_resource_id
  add_item_resource_id = module.api_gateway.add_item_resource_id
  update_user_settings_resource_id = module.api_gateway.update_user_settings_resource_id
  remove_profile_picture_resource_id = module.api_gateway.remove_profile_picture_resource_id
  guest_register_resource_id         = module.api_gateway.guest_register_resource_id
  guest_verify_resource_id           = module.api_gateway.guest_verify_resource_id
  guest_verify_splash_resource_id    = module.api_gateway.guest_verify_splash_resource_id
  recycle_verification_code_resource_id = module.api_gateway.recycle_verification_code_resource_id
  # Construct the execution ARN needed for Lambda permissions
  api_gateway_execution_arn = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${module.api_gateway.api_gateway_id}"

  # S3 bucket name for item images (using existing profile pictures bucket)
  s3_bucket_name = "omatala-online-profile-pictures-dev"
  
  # Pass DynamoDB table info to Lambda
  users_table_name             = module.dynamo_db.users_table_name
  sessions_table_name          = module.dynamo_db.sessions_table_name
  verification_codes_table_name = module.dynamo_db.verification_codes_table_name
  verification_codes_recycle_table_name = module.dynamo_db.verification_codes_recycle_table_name
  deleted_accounts_table_name  = module.dynamo_db.deleted_accounts_table_name
  items_table_name             = module.dynamo_db.items_table_name
  categories_table_name        = module.dynamo_db.categories_table_name
  notifications_table_name     = module.dynamo_db.notifications_table_name
  guests_table_name            = module.dynamo_db.guests_table_name
  phone_index_name             = module.dynamo_db.phone_number_index_name
  stall_name_index_name        = module.dynamo_db.stall_name_index_name
  user_id_index_name           = module.dynamo_db.user_id_index_name
  items_user_id_index_name     = module.dynamo_db.items_user_id_index_name
  deleted_accounts_phone_index_name = module.dynamo_db.deleted_accounts_phone_index_name
  notifications_user_id_index_name = module.dynamo_db.notifications_user_id_index_name
  notifications_user_id_is_read_index_name = module.dynamo_db.notifications_user_id_is_read_index_name



  # Using custom auth instead of Cognito
  # cognito_authorizer_id = ""  # Commented out

  # Pass SNS Topic ARN to Lambda module
  sms_verification_topic_arn = aws_sns_topic.sms_verification.arn

  # Ensure DynamoDB table is created before Lambda
  depends_on = [module.dynamo_db, module.api_gateway]
}


