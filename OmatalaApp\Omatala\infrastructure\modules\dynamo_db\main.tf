# Create a DynamoDB table for users
resource "aws_dynamodb_table" "users_table" {
  name         = "${var.project_name}-users-${var.environment}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "userId"

  attribute {
    name = "userId"
    type = "S"
  }

  attribute {
    name = "phoneNumber"
    type = "S"
  }

  attribute {
    name = "stallName"
    type = "S"
  }



  # Profile image URL will be stored here
  # attribute {
  #   name = "profileImage"
  #   type = "S"
  # }

  global_secondary_index {
    name               = "PhoneNumberIndex"
    hash_key           = "phoneNumber"
    projection_type    = "ALL"
  }

  global_secondary_index {
    name               = "StallNameIndex"
    hash_key           = "stallName"
    projection_type    = "ALL"
  }



  tags = {
    Environment = var.environment
    Project     = var.project_name
  }
}

# Create a DynamoDB table for sessions
resource "aws_dynamodb_table" "sessions_table" {
  name         = "${var.project_name}-sessions-${var.environment}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "sessionToken"

  attribute {
    name = "sessionToken"
    type = "S"
  }

  attribute {
    name = "userId"
    type = "S"
  }

  global_secondary_index {
    name            = "UserIdIndex"
    hash_key        = "userId"
    projection_type = "ALL"
  }

  ttl {
    attribute_name = "expiresAt"
    enabled        = false
  }

  tags = {
    Environment = var.environment
    Project     = var.project_name
  }
}

# Create a DynamoDB table for verification codes
resource "aws_dynamodb_table" "verification_codes_table" {
  name         = "${var.project_name}-verification-codes-${var.environment}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "phoneNumber"

  attribute {
    name = "phoneNumber"
    type = "S"
  }

  ttl {
    attribute_name = "expiresAt"
    enabled        = true  # Enable TTL for automatic deletion
  }

  tags = {
    Environment = var.environment
    Project     = var.project_name
  }
}

# Create a DynamoDB table for verification codes recycle (exact copy of verification_codes_table)
resource "aws_dynamodb_table" "verification_codes_recycle_table" {
  name         = "${var.project_name}-verification-codes-recycle-${var.environment}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "phoneNumber"

  attribute {
    name = "phoneNumber"
    type = "S"
  }

  ttl {
    attribute_name = "expiresAt"
    enabled        = true  # Enable TTL for automatic deletion
  }

  tags = {
    Environment = var.environment
    Project     = var.project_name
  }
}

# Create a DynamoDB table for deleted accounts
resource "aws_dynamodb_table" "deleted_accounts_table" {
  name         = "${var.project_name}-deleted-accounts-${var.environment}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "userId"

  attribute {
    name = "userId"
    type = "S"
  }

  attribute {
    name = "phoneNumber"
    type = "S"
  }

  global_secondary_index {
    name               = "PhoneNumberIndex"
    hash_key           = "phoneNumber"
    projection_type    = "ALL"
  }

  tags = {
    Environment = var.environment
    Project     = var.project_name
  }
}

# Create a DynamoDB table for items
resource "aws_dynamodb_table" "items_table" {
  name         = "${var.project_name}-items-${var.environment}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "itemId"

  attribute {
    name = "itemId"
    type = "S"
  }

  attribute {
    name = "userId"
    type = "S"
  }

  attribute {
    name = "category"
    type = "S"
  }

  attribute {
    name = "region"
    type = "S"
  }

  attribute {
    name = "itemVisibility"
    type = "S"
  }

  # Global Secondary Index to query items by userId
  global_secondary_index {
    name               = "UserIdIndex"
    hash_key           = "userId"
    projection_type    = "ALL"
  }

  # Global Secondary Index to query items by category
  global_secondary_index {
    name               = "CategoryIndex"
    hash_key           = "category"
    projection_type    = "ALL"
  }

  # Global Secondary Index to query items by region
  global_secondary_index {
    name               = "RegionIndex"
    hash_key           = "region"
    projection_type    = "ALL"
  }

  # Global Secondary Index to query items by visibility
  global_secondary_index {
    name               = "VisibilityIndex"
    hash_key           = "itemVisibility"
    projection_type    = "ALL"
  }

  tags = {
    Environment = var.environment
    Project     = var.project_name
  }
}

# Create a DynamoDB table for categories
resource "aws_dynamodb_table" "categories_table" {
  name         = "${var.project_name}-categories-${var.environment}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "categories"

  attribute {
    name = "categories"
    type = "S"
  }

  tags = {
    Environment = var.environment
    Project     = var.project_name
  }
}

# Create a DynamoDB table for notifications
resource "aws_dynamodb_table" "notifications_table" {
  name         = "${var.project_name}-notifications-${var.environment}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "notificationId"

  attribute {
    name = "notificationId"
    type = "S"
  }

  attribute {
    name = "userId"
    type = "S"
  }

  attribute {
    name = "createdAt"
    type = "S"
  }

  attribute {
    name = "isRead"
    type = "S"
  }

  # Global Secondary Index to query notifications by userId
  global_secondary_index {
    name               = "UserIdIndex"
    hash_key           = "userId"
    range_key          = "createdAt"
    projection_type    = "ALL"
  }

  # Global Secondary Index to query notifications by userId and read status
  global_secondary_index {
    name               = "UserIdIsReadIndex"
    hash_key           = "userId"
    range_key          = "isRead"
    projection_type    = "ALL"
  }

  tags = {
    Environment = var.environment
    Project     = var.project_name
  }
}

# Create a DynamoDB table for guests
resource "aws_dynamodb_table" "guests_table" {
  name         = "${var.project_name}-guests-${var.environment}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "guestId"

  attribute {
    name = "guestId"
    type = "S"
  }

  ttl {
    attribute_name = "expiresAt"
    enabled        = true  # Enable TTL for automatic deletion after 20 days
  }

  tags = {
    Environment = var.environment
    Project     = var.project_name
  }
}





