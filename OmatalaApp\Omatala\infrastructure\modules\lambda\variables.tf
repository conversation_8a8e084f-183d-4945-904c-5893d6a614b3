variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "environment" {
  description = "Environment (dev, prod)"
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "account_id" {
  description = "AWS account ID"
  type        = string
}

variable "users_table_name" {
  description = "DynamoDB users table name"
  type        = string
}

variable "sessions_table_name" {
  description = "DynamoDB sessions table name"
  type        = string
}

variable "verification_codes_table_name" {
  description = "DynamoDB verification codes table name"
  type        = string
}

variable "verification_codes_recycle_table_name" {
  description = "DynamoDB verification codes recycle table name"
  type        = string
}

variable "phone_index_name" {
  description = "DynamoDB phone number index name"
  type        = string
}

variable "stall_name_index_name" {
  description = "DynamoDB stall name index name"
  type        = string
}

variable "user_id_index_name" {
  description = "The name of the user ID GSI for the Users table"
  type        = string
}

variable "api_gateway_id" {
  description = "API Gateway ID"
  type        = string
}

variable "api_root_resource_id" {
  description = "The root resource ID of the API Gateway"
  type        = string
}



variable "api_gateway_execution_arn" {
  description = "The execution ARN of the API Gateway"
  type        = string
}



variable "api_gateway_root_id" {
  description = "API Gateway root resource ID (same as api_root_resource_id)"
  type        = string
  default     = ""
}

variable "sms_verification_topic_arn" {
  description = "The ARN of the SNS topic for sending SMS verification codes"
  type        = string
}

variable "deleted_accounts_table_name" {
  description = "The name of the deleted accounts DynamoDB table"
  type        = string
}

variable "deleted_accounts_phone_index_name" {
  description = "The name of the phone number index on the deleted accounts table"
  type        = string
}

variable "items_table_name" {
  description = "DynamoDB items table name"
  type        = string
}

variable "items_user_id_index_name" {
  description = "The name of the user ID GSI for the Items table"
  type        = string
}

variable "categories_table_name" {
  description = "DynamoDB categories table name"
  type        = string
}

variable "recycle_verification_code_resource_id" {
  description = "API Gateway resource ID for recycle verification code endpoint"
  type        = string
}

variable "api_gateway_rest_api_id" {
  description = "API Gateway REST API ID"
  type        = string
}

variable "check_phone_resource_id" {
  description = "The resource ID for the check-phone endpoint"
  type        = string
}

variable "send_code_resource_id" {
  description = "The resource ID for the send-code endpoint"
  type        = string
}

variable "verify_code_resource_id" {
  description = "The resource ID for the verify-code endpoint"
  type        = string
}

variable "check_pending_code_resource_id" {
  description = "The resource ID for the check-pending-code endpoint"
  type        = string
}

variable "register_user_resource_id" {
  description = "The resource ID for the register-user endpoint"
  type        = string
}

variable "check_session_resource_id" {
  description = "The resource ID for the check-session endpoint"
  type        = string
}

variable "get_stall_data_resource_id" {
  description = "The resource ID of the get-stall-data API Gateway endpoint"
  type        = string
}

variable "s3_bucket_name" {
  description = "The name of the S3 bucket for storing item images"
  type        = string
}

variable "add_item_resource_id" {
  description = "The resource ID for the add-item endpoint"
  type        = string
}

variable "update_user_settings_resource_id" {
  description = "The resource ID for the update-user-settings endpoint"
  type        = string
}

variable "remove_profile_picture_resource_id" {
  description = "The resource ID for the remove-profile-picture endpoint"
  type        = string
}

variable "notifications_table_name" {
  description = "DynamoDB notifications table name"
  type        = string
}

variable "guests_table_name" {
  description = "DynamoDB guests table name"
  type        = string
}

variable "notifications_user_id_index_name" {
  description = "The name of the UserIdIndex GSI for the Notifications table"
  type        = string
}

variable "notifications_user_id_is_read_index_name" {
  description = "The name of the UserIdIsReadIndex GSI for the Notifications table"
  type        = string
}

variable "guest_register_resource_id" {
  description = "The resource ID of the guest-register endpoint"
  type        = string
}

variable "guest_verify_resource_id" {
  description = "The resource ID of the guest-verify endpoint"
  type        = string
}

