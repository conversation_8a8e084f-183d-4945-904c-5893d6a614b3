

# Create IAM role for Lambda execution
resource "aws_iam_role" "lambda_role" {
  name = "${var.project_name}-auth-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

# Attach basic execution policies to the Lambda role
resource "aws_iam_role_policy_attachment" "lambda_basic" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# Remove the overly permissive DynamoDB full access
# resource "aws_iam_role_policy_attachment" "lambda_dynamo" {
#   role       = aws_iam_role.lambda_role.name
#   policy_arn = "arn:aws:iam::aws:policy/AmazonDynamoDBFullAccess"
# }

# Create explicit IAM policy for check-phone Lambda to access DynamoDB
resource "aws_iam_policy" "check_phone_dynamodb_policy" {
  name        = "${var.project_name}-check-phone-dynamodb-${var.environment}"
  description = "Allow check-phone Lambda to access DynamoDB tables"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = [
          "dynamodb:Query",
          "dynamodb:GetItem",
          "dynamodb:BatchGetItem",
          "dynamodb:Scan"
        ]
        Resource = [
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.users_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.users_table_name}/index/${var.phone_index_name}"
        ]
      }
    ]
  })
}

# Create explicit IAM policy for send-code Lambda to access DynamoDB
resource "aws_iam_policy" "send_code_dynamodb_policy" {
  name        = "${var.project_name}-send-code-dynamodb-${var.environment}"
  description = "Allow send-code Lambda to access DynamoDB tables"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = [
          "dynamodb:Query",
          "dynamodb:GetItem",
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:BatchGetItem"
        ]
        Resource = [
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.users_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.users_table_name}/index/${var.phone_index_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.verification_codes_table_name}"
        ]
      }
    ]
  })
}

# Create explicit IAM policy for verify-code Lambda to access DynamoDB
resource "aws_iam_policy" "verify_code_dynamodb_policy" {
  name        = "${var.project_name}-verify-code-dynamodb-${var.environment}"
  description = "Allow verify-code and recycle-verification-code Lambdas to access DynamoDB tables"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = [
          "dynamodb:Query",
          "dynamodb:GetItem",
          "dynamodb:PutItem",
          "dynamodb:DeleteItem",
          "dynamodb:UpdateItem",
          "dynamodb:BatchGetItem"
        ]
        Resource = [
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.users_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.users_table_name}/index/${var.phone_index_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.verification_codes_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.verification_codes_recycle_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.sessions_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.sessions_table_name}/index/${var.user_id_index_name}"
        ]
      }
    ]
  })
}

# Create explicit IAM policy for register-user Lambda to access DynamoDB
resource "aws_iam_policy" "register_user_dynamodb_policy" {
  name        = "${var.project_name}-register-user-dynamodb-${var.environment}"
  description = "Allow register-user Lambda to access DynamoDB tables"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = [
          "dynamodb:Query",
          "dynamodb:GetItem",
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:BatchGetItem"
        ]
        Resource = [
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.users_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.users_table_name}/index/${var.phone_index_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.users_table_name}/index/${var.stall_name_index_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.sessions_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.deleted_accounts_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.deleted_accounts_table_name}/index/${var.deleted_accounts_phone_index_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.items_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.items_table_name}/index/${var.items_user_id_index_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.categories_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.verification_codes_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.verification_codes_recycle_table_name}"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:GetObject",
          "s3:DeleteObject"
        ]
        Resource = [
          "arn:aws:s3:::${var.s3_bucket_name}/*"
        ]
      }
    ]
  })
}

# Create explicit IAM policy for check-session and get-categories Lambdas to access DynamoDB
resource "aws_iam_policy" "check_session_dynamodb_policy" {
  name        = "${var.project_name}-check-session-dynamodb-${var.environment}"
  description = "Allow check-session and get-categories Lambdas to access DynamoDB tables"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = [
          "dynamodb:Query",
          "dynamodb:GetItem",
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:BatchGetItem",
          "dynamodb:Scan"
        ]
        Resource = [
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.users_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.sessions_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.categories_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.guests_table_name}"
        ]
      }
    ]
  })
}

# Create explicit IAM policy for get-stall-data Lambda to access DynamoDB
resource "aws_iam_policy" "get_stall_data_dynamodb_policy" {
  name        = "${var.project_name}-get-stall-data-dynamodb-${var.environment}"
  description = "Allow get-stall-data Lambda to access DynamoDB tables"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = [
          "dynamodb:Query",
          "dynamodb:GetItem",
          "dynamodb:BatchGetItem"
        ]
        Resource = [
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.users_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.sessions_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.items_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.items_table_name}/index/${var.items_user_id_index_name}"
        ]
      }
    ]
  })
}

# Create explicit IAM policy for migrate-items Lambda to access DynamoDB
resource "aws_iam_policy" "migrate_items_dynamodb_policy" {
  name        = "${var.project_name}-migrate-items-dynamodb-${var.environment}"
  description = "Allow migrate-items Lambda to access DynamoDB tables"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "dynamodb:Scan",
          "dynamodb:UpdateItem"
        ]
        Resource = [
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.items_table_name}"
        ]
      }
    ]
  })
}

# Guest table permissions are now included in the check_session_dynamodb_policy above

# Attach policies to the Lambda role
resource "aws_iam_role_policy_attachment" "check_phone_dynamodb_attach" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.check_phone_dynamodb_policy.arn
}

resource "aws_iam_role_policy_attachment" "send_code_dynamodb_attach" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.send_code_dynamodb_policy.arn
}

resource "aws_iam_role_policy_attachment" "verify_code_dynamodb_attach" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.verify_code_dynamodb_policy.arn
}



resource "aws_iam_role_policy_attachment" "register_user_dynamodb_attach" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.register_user_dynamodb_policy.arn
}

resource "aws_iam_role_policy_attachment" "check_session_dynamodb_attach" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.check_session_dynamodb_policy.arn
}

resource "aws_iam_role_policy_attachment" "get_stall_data_dynamodb_attach" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.get_stall_data_dynamodb_policy.arn
}

resource "aws_iam_role_policy_attachment" "migrate_items_dynamodb_attach" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.migrate_items_dynamodb_policy.arn
}

# Guest table permissions are now included in the check_session_dynamodb_policy

# SNS Publish Policy
resource "aws_iam_policy" "sns_publish_policy" {
  name        = "${var.project_name}-sns-publish-${var.environment}"
  description = "Allow publishing to the SMS verification SNS topic"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "sns:Publish"
        Resource = "*" # Allow publishing to all SNS resources, including direct phone numbers
      }
    ]
  })
}

# API Gateway invoke permission policy
resource "aws_iam_policy" "api_gateway_lambda_invoke_policy" {
  name        = "${var.project_name}-api-gateway-lambda-invoke-${var.environment}"
  description = "Allow API Gateway to invoke Lambda functions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "lambda:InvokeFunction"
        Resource = "*" # For all Lambda functions in this project
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "lambda_sns_publish" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.sns_publish_policy.arn
}

resource "aws_iam_role_policy_attachment" "api_gateway_lambda_invoke" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.api_gateway_lambda_invoke_policy.arn
}

# --- Phone Number Check Lambda ---

# Create a zip file for the check-phone function
data "archive_file" "check_phone_code" {
  type        = "zip"
  output_path = "${path.module}/check_phone_function.zip"
  
  source {
    content  = <<-EOF
    const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
    const { DynamoDBDocumentClient, QueryCommand } = require("@aws-sdk/lib-dynamodb");
    
    const dbClient = new DynamoDBClient({
      region: process.env.AWS_REGION
    });
    const docClient = DynamoDBDocumentClient.from(dbClient);
    
    const usersTableName = process.env.USERS_TABLE_NAME;
    const phoneIndexName = process.env.PHONE_INDEX_NAME;
    
    exports.handler = async (event) => {
      console.log("Received event:", JSON.stringify(event, null, 2));
      console.log("Environment:", JSON.stringify(process.env, null, 2));
      console.log("DynamoDB config:", {
        usersTableName,
        phoneIndexName,
        region: process.env.AWS_REGION
      });

      const headers = {
        "Access-Control-Allow-Origin": "*", // Adjust in production
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "OPTIONS,POST"
      };

      // Handle CORS preflight requests
      if (event.httpMethod === 'OPTIONS') {
        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({ message: 'CORS preflight successful' })
        };
      }

      if (event.httpMethod !== 'POST') {
          console.log("Error: Received " + event.httpMethod + " request but expected POST");
          return {
              statusCode: 405,
              headers: headers,
              body: JSON.stringify({ message: 'Method Not Allowed' }),
          };
      }

      let phoneNumber;
      try {
        if (!event.body) {
            throw new Error("Request body is missing");
        }
        const body = JSON.parse(event.body);
        phoneNumber = body.phoneNumber;
        if (!phoneNumber) {
          throw new Error("Missing 'phoneNumber' in request body");
        }
        // Basic validation (can be improved)
        if (!/^\+?[1-9]\d{1,14}$/.test(phoneNumber)) {
           throw new Error("Invalid phone number format.");
        }
        console.log("Checking phone number: " + phoneNumber);
      } catch (error) {
        console.error("Input error:", error);
        return {
          statusCode: 400,
          headers: headers,
          body: JSON.stringify({ message: "Invalid input: " + error.message }),
        };
      }

      const params = {
        TableName: usersTableName,
        IndexName: phoneIndexName,
        KeyConditionExpression: "phoneNumber = :phone",
        ExpressionAttributeValues: {
          ":phone": phoneNumber,
        },
      };

      try {
        console.log("Querying DynamoDB: Table=" + usersTableName + ", Index=" + phoneIndexName + ", Key=" + phoneNumber);
        console.log("QueryCommand params:", JSON.stringify(params, null, 2));
        
        const command = new QueryCommand(params);
        console.log("Executing DynamoDB query...");
        
        const data = await docClient.send(command);
        console.log("DynamoDB response:", JSON.stringify(data, null, 2));

        const userExists = data.Items && data.Items.length > 0;
        console.log("User exists: " + userExists);

        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({ userExists: userExists }),
        };
      } catch (error) {
        console.error("DynamoDB query error:", error);
        return {
          statusCode: 500,
          headers: headers,
          body: JSON.stringify({ 
            message: "Error checking phone number",
            error: error.message,
            code: error.code
          }),
        };
      }
    };
    EOF
    filename = "index.js"
  }
}

# Create the Lambda function for phone number check
resource "aws_lambda_function" "check_phone_lambda" {
  function_name    = "${var.project_name}-check-phone-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.check_phone_code.output_path
  source_code_hash = data.archive_file.check_phone_code.output_base64sha256

  environment {
    variables = {
      USERS_TABLE_NAME = var.users_table_name
      PHONE_INDEX_NAME = var.phone_index_name
      ENVIRONMENT      = var.environment
    }
  }
}

# --- Send Verification Code Lambda ---

# Create a zip file for the send-code function
data "archive_file" "send_code_lambda" {
  type        = "zip"
  output_path = "${path.module}/send_code_function.zip"
  source {
    content  = <<-EOF
    const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
    const { DynamoDBDocumentClient, PutCommand, QueryCommand } = require("@aws-sdk/lib-dynamodb");
    const { SNSClient, PublishCommand } = require("@aws-sdk/client-sns"); // Import SNS client

    // Version 1.0.1 - Fixed string interpolation and SNS permissions
    
    const dbClient = new DynamoDBClient({});
    const docClient = DynamoDBDocumentClient.from(dbClient);
    const snsClient = new SNSClient({}); // Initialize SNS client

    const usersTableName = process.env.USERS_TABLE_NAME;
    const phoneIndexName = process.env.PHONE_INDEX_NAME;
    const codesTableName = process.env.VERIFICATION_CODES_TABLE_NAME;
    const snsTopicArn = process.env.SNS_TOPIC_ARN; // Get SNS Topic ARN

    // Generate a random verification code
    function generateVerificationCode() {
      return Math.floor(100000 + Math.random() * 900000).toString();
    }

    exports.handler = async (event) => {
      console.log("Received event:", JSON.stringify(event, null, 2));

      const headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "OPTIONS,POST"
      };

      if (event.httpMethod === 'OPTIONS') {
        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({ message: 'CORS preflight successful' })
        };
      }

      if (event.httpMethod !== 'POST') {
        return {
          statusCode: 405,
          headers: headers,
          body: JSON.stringify({ message: 'Method Not Allowed' }),
        };
      }

      let phoneNumber;
      try {
        if (!event.body) {
            throw new Error("Request body is missing");
        }
        const body = JSON.parse(event.body);
        phoneNumber = body.phoneNumber;
        if (!phoneNumber) {
          throw new Error("Missing 'phoneNumber' in request body");
        }
        // Ensure phone number is in E.164 format for SNS
        if (!/^\+[1-9]\d{1,14}$/.test(phoneNumber)) {
           throw new Error("Invalid phone number format. Must be E.164 (e.g., +1234567890).");
        }
        if (!snsTopicArn) {
          throw new Error("SNS Topic ARN is not configured in environment variables.");
        }
      } catch (error) {
        console.error("Input error:", error);
        return {
          statusCode: 400,
          headers: headers,
          body: JSON.stringify({ message: "Invalid input: " + error.message }),
        };
      }

      const verificationCode = generateVerificationCode();
      const ttl = Math.floor(Date.now() / 1000) + 300; // 5 minutes expiry
      const expiresAt = new Date(ttl * 1000).toISOString();

      // Check if user exists
      let isExistingUser = false;
      try {
          const userQuery = new QueryCommand({
              TableName: usersTableName,
              IndexName: phoneIndexName,
              KeyConditionExpression: "phoneNumber = :phone",
              ExpressionAttributeValues: { ":phone": phoneNumber },
          });
          const userData = await docClient.send(userQuery);
          isExistingUser = userData.Items && userData.Items.length > 0;
          console.log(`User check completed. Existing: $${isExistingUser}`);
      } catch (error) {
          console.error("Error checking user existence:", error);
          // Decide if this should be fatal or just log & continue
          return {
              statusCode: 500,
              headers: headers,
              body: JSON.stringify({ message: "Error checking user status" })
          };
      }

      // Save code to DynamoDB with timestamps
      const now = Math.floor(Date.now() / 1000);
      const putParams = {
        TableName: codesTableName,
        Item: {
          phoneNumber: phoneNumber,
          code: verificationCode,
          createdAt: now,           // Add created timestamp
          expiresAt: ttl,           // Use expiresAt instead of ttl for consistency
        },
      };

      try {
        console.log(`Saving verification code to $${codesTableName}`);
        await docClient.send(new PutCommand(putParams));
        console.log("Verification code saved successfully.");

        // Send SMS via SNS
        const smsMessage = `Your Omatala verification code is: $${verificationCode}`; 
        const snsParams = {
          Message: smsMessage,
          PhoneNumber: phoneNumber,
          // Optional: Add MessageAttributes for SenderID etc. if needed and configured
          // MessageAttributes: {
          //   'AWS.SNS.SMS.SenderID': { DataType: 'String', StringValue: 'OmatalaApp' },
          //   'AWS.SNS.SMS.SMSType': { DataType: 'String', StringValue: 'Transactional' }
          // }
        };

        console.log(`Sending SMS via SNS to: $${phoneNumber}`);
        const publishCommand = new PublishCommand(snsParams);
        const snsResponse = await snsClient.send(publishCommand);
        console.log("SNS publish response:", snsResponse);

        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({ 
              success: true, 
              message: "Verification code sent successfully.",
              expiresAt: expiresAt,
              isExisting: isExistingUser
          }),
        };
      } catch (error) {
        console.error("Error saving code or sending SMS:", error);
        // Check if the error is from SNS
        if (error.name === 'AuthorizationErrorException') {
             console.error("SNS Authorization Error: Check Lambda IAM permissions for sns:Publish on the topic.");
        } else if (error.name === 'InvalidParameterException' && error.message.includes('PhoneNumber')) {
            console.error("SNS Invalid Phone Number: Ensure number is E.164 format.");
        } else {
            // General DynamoDB or other errors
            console.error("Could not save verification code or send SMS:", error);
        }

        return {
          statusCode: 500,
          headers: headers,
          body: JSON.stringify({ 
              success: false, 
              message: "Failed to send verification code.",
              isExisting: isExistingUser // Still return user status if known
          }),
        };
      }
    };
    EOF
    filename = "index.js"
  }
}

# Create the Lambda function for sending verification code
resource "aws_lambda_function" "send_code_lambda" {
  function_name    = "${var.project_name}-send-code-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.send_code_lambda.output_path
  source_code_hash = data.archive_file.send_code_lambda.output_base64sha256
  timeout          = 30 # Increase timeout slightly for SNS call

  environment {
    variables = {
      USERS_TABLE_NAME             = var.users_table_name
      PHONE_INDEX_NAME             = var.phone_index_name
      VERIFICATION_CODES_TABLE_NAME = var.verification_codes_table_name
      ENVIRONMENT                  = var.environment
      SNS_TOPIC_ARN                = var.sms_verification_topic_arn # Add SNS Topic ARN here
    }
  }
  # Ensure SNS policy is attached before lambda creation
  depends_on = [aws_iam_role_policy_attachment.lambda_sns_publish]
}

# --- Verify Code Lambda ---

# Create a zip file for the verify-code function
data "archive_file" "verify_code_lambda" {
  type        = "zip"
  output_path = "${path.module}/verify_code_function.zip"
  source {
    content  = <<-EOF
    const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
    const { DynamoDBDocumentClient, GetCommand, QueryCommand, PutCommand, DeleteCommand, UpdateCommand } = require("@aws-sdk/lib-dynamodb");
    const crypto = require('crypto');

    const client = new DynamoDBClient({});
    const docClient = DynamoDBDocumentClient.from(client);
    const usersTableName = process.env.USERS_TABLE_NAME;
    const phoneIndexName = process.env.PHONE_INDEX_NAME;
    const codesTableName = process.env.VERIFICATION_CODES_TABLE_NAME;
    const codesRecycleTableName = process.env.VERIFICATION_CODES_RECYCLE_TABLE_NAME;
    const sessionsTableName = process.env.SESSIONS_TABLE_NAME;
    const userIdIndexName = process.env.USER_ID_INDEX_NAME;

    exports.handler = async (event) => {
      console.log("Received event:", JSON.stringify(event, null, 2));

      const headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "OPTIONS,POST"
      };

      // Handle CORS preflight requests
      if (event.httpMethod === 'OPTIONS') {
        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({ message: 'CORS preflight successful' })
        };
      }

      if (event.httpMethod !== 'POST') {
        return {
          statusCode: 405,
          headers: headers,
          body: JSON.stringify({ message: 'Method Not Allowed' }),
        };
      }

      let phoneNumber, code;
      try {
        if (!event.body) {
          throw new Error("Request body is missing");
        }
        const body = JSON.parse(event.body);
        phoneNumber = body.phoneNumber;
        code = body.code;
        
        if (!phoneNumber) {
          throw new Error("Missing 'phoneNumber' in request body");
        }
        if (!code) {
          throw new Error("Missing 'code' in request body");
        }
      } catch (error) {
        console.error("Input error:", error);
        return {
          statusCode: 400,
          headers: headers,
          body: JSON.stringify({ message: "Invalid input: " + error.message }),
        };
      }

      try {
        // Get the stored verification code
        const getCodeParams = {
          TableName: codesTableName,
          Key: {
            phoneNumber: phoneNumber
          }
        };
        
        const codeResult = await docClient.send(new GetCommand(getCodeParams));
        const storedCode = codeResult.Item;
        
        // Verify code exists and is valid
        if (!storedCode) {
          return {
            statusCode: 400,
            headers: headers,
            body: JSON.stringify({ 
              success: false, 
              message: "No verification code found for this phone number"
            }),
          };
        }
        
        // Check if code has expired
        const now = Math.floor(Date.now() / 1000);
        if (storedCode.expiresAt < now) {
          // Delete expired code
          await docClient.send(new DeleteCommand(getCodeParams));
          
          return {
            statusCode: 400,
            headers: headers,
            body: JSON.stringify({ 
              success: false, 
              message: "Verification code has expired"
            }),
          };
        }
        
        // Check if code matches
        if (storedCode.code !== code) {
          return {
            statusCode: 400,
            headers: headers,
            body: JSON.stringify({ 
              success: false, 
              message: "Invalid verification code"
            }),
          };
        }
        
        // Fetch user from database
        const userParams = {
          TableName: usersTableName,
          IndexName: phoneIndexName,
          KeyConditionExpression: "phoneNumber = :phone",
          ExpressionAttributeValues: {
            ":phone": phoneNumber,
          },
        };
        
        const userCommand = new QueryCommand(userParams);
        const userData = await docClient.send(userCommand);
        
        if (!userData.Items || userData.Items.length === 0) {
          return {
            statusCode: 404,
            headers: headers,
            body: JSON.stringify({ 
              success: false, 
              message: "User not found"
            }),
          };
        }
        
        const user = userData.Items[0];
        
        // Delete existing sessions for this user
        const existingSessionsParams = {
          TableName: sessionsTableName,
          IndexName: userIdIndexName,
          KeyConditionExpression: "userId = :userId",
          ExpressionAttributeValues: {
            ":userId": user.userId,
          },
        };
        
        const existingSessions = await docClient.send(new QueryCommand(existingSessionsParams));
        
        // Delete each existing session
        if (existingSessions.Items && existingSessions.Items.length > 0) {
          for (const session of existingSessions.Items) {
            await docClient.send(new DeleteCommand({
              TableName: sessionsTableName,
              Key: {
                sessionToken: session.sessionToken
              }
            }));
          }
        }
        
        // Create new session
        const sessionToken = crypto.randomBytes(32).toString('hex');
        // Session expires in 30 days
        const sessionExpiration = Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60);
        
        const sessionParams = {
          TableName: sessionsTableName,
          Item: {
            sessionToken: sessionToken,
            userId: user.userId,
            phoneNumber: phoneNumber,
            createdAt: Math.floor(Date.now() / 1000),
            expiresAt: sessionExpiration
          }
        };
        
        await docClient.send(new PutCommand(sessionParams));

        // Move the used verification code to recycle table before deleting from main table
        const recycleParams = {
          TableName: codesRecycleTableName,
          Item: {
            phoneNumber: storedCode.phoneNumber,
            code: storedCode.code,
            createdAt: storedCode.createdAt,
            expiresAt: storedCode.expiresAt,
            verifiedAt: Math.floor(Date.now() / 1000) // Add timestamp when code was verified
          }
        };

        await docClient.send(new PutCommand(recycleParams));

        // Clean up the used verification code from main table
        await docClient.send(new DeleteCommand(getCodeParams));
        
        // Return success with the new session token
        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({ 
            success: true, 
            message: "Code verified successfully",
            sessionToken: sessionToken,
            user: {
              userId: user.userId,
              name: user.name,
              phoneNumber: user.phoneNumber,
              stallName: user.stallName,
            }
          }),
        };
        
      } catch (error) {
        console.error("Error verifying code:", error);
        return {
          statusCode: 500,
          headers: headers,
          body: JSON.stringify({ message: "Error verifying code" }),
        };
      }
    };
    EOF
    filename = "index.js"
  }
}

# --- Check Pending Code Lambda ---
data "archive_file" "check_pending_code_lambda" {
  type        = "zip"
  output_path = "modules/lambda/check_pending_code.zip"

  source {
    content = <<EOF
    const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
    const { DynamoDBDocumentClient, GetCommand } = require("@aws-sdk/lib-dynamodb");

    const dbClient = new DynamoDBClient({});
    const docClient = DynamoDBDocumentClient.from(dbClient);

    exports.handler = async (event) => {
      console.log("Check pending code triggered with event:", JSON.stringify(event, null, 2));

      const headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      };

      if (event.httpMethod === 'OPTIONS') {
        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({ message: 'CORS preflight successful' })
        };
      }

      try {
        const body = JSON.parse(event.body);
        const phoneNumber = body.phoneNumber;

        if (!phoneNumber) {
          return {
            statusCode: 400,
            headers: headers,
            body: JSON.stringify({
              success: false,
              message: "Phone number is required"
            }),
          };
        }

        const codesTableName = process.env.VERIFICATION_CODES_TABLE_NAME;

        // Check for existing verification code
        const getCodeParams = {
          TableName: codesTableName,
          Key: {
            phoneNumber: phoneNumber
          }
        };

        const codeResult = await docClient.send(new GetCommand(getCodeParams));
        const storedCode = codeResult.Item;

        if (!storedCode) {
          // No pending code found
          return {
            statusCode: 200,
            headers: headers,
            body: JSON.stringify({
              success: true,
              pending: false,
              message: "No pending verification code found"
            }),
          };
        }

        // Check if code has expired
        const now = Math.floor(Date.now() / 1000);
        if (storedCode.expiresAt <= now) {
          // Code has expired, delete it
          const { DeleteCommand } = require("@aws-sdk/lib-dynamodb");
          await docClient.send(new DeleteCommand(getCodeParams));

          return {
            statusCode: 200,
            headers: headers,
            body: JSON.stringify({
              success: true,
              pending: false,
              message: "Previous code expired"
            }),
          };
        }

        // Calculate remaining time
        const remainingTime = storedCode.expiresAt - now;

        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({
            success: true,
            pending: true,
            remainingTime: remainingTime,
            message: "Pending verification code found"
          }),
        };

      } catch (error) {
        console.error("Error checking pending code:", error);
        return {
          statusCode: 500,
          headers: headers,
          body: JSON.stringify({
            success: false,
            message: "Error checking pending code"
          }),
        };
      }
    };
    EOF
    filename = "index.js"
  }
}

# Create the Lambda function for checking pending codes
resource "aws_lambda_function" "check_pending_code_lambda" {
  function_name    = "${var.project_name}-check-pending-code-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.check_pending_code_lambda.output_path
  source_code_hash = data.archive_file.check_pending_code_lambda.output_base64sha256
  timeout          = 30

  environment {
    variables = {
      VERIFICATION_CODES_TABLE_NAME = var.verification_codes_table_name
      ENVIRONMENT                   = var.environment
    }
  }
}

# API Gateway Integration for check-pending-code
resource "aws_api_gateway_integration" "check_pending_code_post" {
  rest_api_id             = var.api_gateway_id
  resource_id             = var.check_pending_code_resource_id
  http_method             = "POST"
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/${aws_lambda_function.check_pending_code_lambda.arn}/invocations"
}

# Method Response for check-pending-code
resource "aws_api_gateway_method_response" "check_pending_code_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.check_pending_code_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }
}

# Integration Response for check-pending-code
resource "aws_api_gateway_integration_response" "check_pending_code_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.check_pending_code_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }
}

# Lambda Permission for check-pending-code
resource "aws_lambda_permission" "api_gateway_check_pending_code_permission" {
  statement_id  = "AllowAPIGatewayInvokeCheckPendingCode"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.check_pending_code_lambda.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.region}:${var.account_id}:${var.api_gateway_id}/${var.environment}/POST/check-pending-code"
}

# Create the Lambda function for verifying code
resource "aws_lambda_function" "verify_code_lambda" {
  function_name    = "${var.project_name}-verify-code-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.verify_code_lambda.output_path
  source_code_hash = data.archive_file.verify_code_lambda.output_base64sha256

  environment {
    variables = {
      USERS_TABLE_NAME           = var.users_table_name
      PHONE_INDEX_NAME           = var.phone_index_name
      VERIFICATION_CODES_TABLE_NAME = var.verification_codes_table_name
      VERIFICATION_CODES_RECYCLE_TABLE_NAME = var.verification_codes_recycle_table_name
      SESSIONS_TABLE_NAME        = var.sessions_table_name
      USER_ID_INDEX_NAME         = var.user_id_index_name
      ENVIRONMENT                = var.environment
    }
  }
}

# --- Register User Lambda ---

# Create a zip file for the register-user function from the dedicated directory
data "archive_file" "register_user_lambda" {
  type        = "zip"
  source_dir  = "${path.module}/functions/registerUser"
  output_path = "${path.module}/register_user_function.zip"
  excludes    = ["package-lock.json"] # Exclude lock file if present

  # The hash will now depend on the contents of the source_dir
}

# Create the Lambda function for registering a user
resource "aws_lambda_function" "register_user_lambda" {
  function_name    = "${var.project_name}-register-user-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.register_user_lambda.output_path
  source_code_hash = data.archive_file.register_user_lambda.output_base64sha256
  timeout          = 10 # Increased timeout to 10 seconds

  environment {
    variables = {
      USERS_TABLE_NAME                    = var.users_table_name
      PHONE_INDEX_NAME                    = var.phone_index_name
      STALL_NAME_INDEX_NAME               = var.stall_name_index_name
      SESSIONS_TABLE_NAME                 = var.sessions_table_name
      DELETED_ACCOUNTS_TABLE_NAME         = var.deleted_accounts_table_name
      DELETED_ACCOUNTS_PHONE_INDEX_NAME   = var.deleted_accounts_phone_index_name
      ENVIRONMENT                         = var.environment
    }
  }
}

# --- Check Session Lambda ---

# Create a zip file for the check-session function
data "archive_file" "check_session_lambda" {
  type        = "zip"
  output_path = "${path.module}/check_session_function.zip"
  source {
    content  = <<-EOF
    const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
    const { DynamoDBDocumentClient, GetCommand, QueryCommand } = require("@aws-sdk/lib-dynamodb");

    const client = new DynamoDBClient({});
    const docClient = DynamoDBDocumentClient.from(client);
    const usersTableName = process.env.USERS_TABLE_NAME;
    const sessionsTableName = process.env.SESSIONS_TABLE_NAME;

    exports.handler = async (event) => {
      console.log("[DEBUG] Lambda triggered with event:", JSON.stringify(event, null, 2));
      console.log("[DEBUG] Lambda environment:", process.env);

      const headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type,Authorization",
        "Access-Control-Allow-Methods": "OPTIONS,POST"
      };
      
      console.log("[DEBUG] HTTP method received:", event.httpMethod);
      console.log("[DEBUG] Request path:", event.path);
      console.log("[DEBUG] Stage name:", event.requestContext?.stage);

      // Handle CORS preflight requests
      if (event.httpMethod === 'OPTIONS') {
        console.log("[DEBUG] Handling OPTIONS request");
        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({ message: 'CORS preflight successful' })
        };
      }

      if (event.httpMethod !== 'POST') {
        console.log("[DEBUG] Incorrect HTTP method: " + event.httpMethod);
        return {
          statusCode: 405,
          headers: headers,
          body: JSON.stringify({ message: 'Method Not Allowed' }),
        };
      }

      let sessionToken, phoneNumber;
      try {
        // Parse request body to get sessionToken and phoneNumber
        if (!event.body) {
          throw new Error("Request body is missing");
        }
        
        const body = JSON.parse(event.body);
        console.log("Parsed body:", body);
        
        sessionToken = body.sessionToken;
        phoneNumber = body.phoneNumber;
        
        if (!sessionToken) {
          throw new Error("Missing 'sessionToken' in request body");
        }
        
        console.log("Validating session token: " + sessionToken + " for phone: " + phoneNumber);
      } catch (error) {
        console.error("Input error:", error);
        return {
          statusCode: 400,
          headers: headers,
          body: JSON.stringify({ 
            isValid: false,
            message: "Invalid input: " + error.message
          }),
        };
      }
      
      try {
        // Get session from database
        const sessionParams = {
          TableName: sessionsTableName,
          Key: {
            sessionToken: sessionToken
          }
        };
        
        console.log("Querying session:", sessionParams);
        const sessionResult = await docClient.send(new GetCommand(sessionParams));
        const session = sessionResult.Item;
        
        // Check if session exists
        if (!session) {
          return {
            statusCode: 401,
            headers: headers,
            body: JSON.stringify({ 
              isValid: false, 
              message: "Invalid session token"
            }),
          };
        }
        
        // Check if session has expired
        const now = Math.floor(Date.now() / 1000);
        if (session.expiresAt < now) {
          return {
            statusCode: 401,
            headers: headers,
            body: JSON.stringify({ 
              isValid: false, 
              message: "Session has expired"
            }),
          };
        }
        
        // Get user details
        const userParams = {
          TableName: usersTableName,
          Key: {
            userId: session.userId
          }
        };
        
        console.log("Getting user details:", userParams);
        const userResult = await docClient.send(new GetCommand(userParams));
        const user = userResult.Item;
        
        if (!user) {
          return {
            statusCode: 404,
            headers: headers,
            body: JSON.stringify({ 
              isValid: false, 
              message: "User not found"
            }),
          };
        }
        
        // Return success with session and user details
        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({ 
            isValid: true,
            user: {
              userId: user.userId,
              name: user.name,
              phoneNumber: user.phoneNumber,
              stallName: user.stallName
            }
          }),
        };
        
      } catch (error) {
        console.error("Error checking session:", error);
        return {
          statusCode: 500,
          headers: headers,
          body: JSON.stringify({ 
            isValid: false,
            message: "Error checking session",
            error: error.message 
          }),
        };
      }
    };
    EOF
    filename = "index.js"
  }
}

# Create the Lambda function for checking a session
resource "aws_lambda_function" "check_session_lambda" {
  function_name    = "${var.project_name}-check-session-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.check_session_lambda.output_path
  source_code_hash = data.archive_file.check_session_lambda.output_base64sha256

  environment {
    variables = {
      USERS_TABLE_NAME    = var.users_table_name
      SESSIONS_TABLE_NAME = var.sessions_table_name
      ENVIRONMENT         = var.environment
    }
  }
}

# --- Add Item Lambda ---

# Create a zip file for the add-item function
data "archive_file" "add_item_lambda" {
  type        = "zip"
  output_path = "${path.module}/add_item_function.zip"
  source {
    content  = <<-EOF
    const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
    const { DynamoDBDocumentClient, PutCommand } = require("@aws-sdk/lib-dynamodb");
    const { S3Client, PutObjectCommand } = require("@aws-sdk/client-s3");
    const crypto = require('crypto');

    const dbClient = new DynamoDBClient({});
    const docClient = DynamoDBDocumentClient.from(dbClient);
    const s3Client = new S3Client({});

    const itemsTableName = process.env.ITEMS_TABLE_NAME;
    const s3BucketName = process.env.S3_BUCKET_NAME;

    exports.handler = async (event) => {
      console.log("Add Item Lambda triggered with event:", JSON.stringify(event, null, 2));

      try {
        // Parse the request body
        const body = JSON.parse(event.body);
        const {
          userId,
          itemName,
          category,
          price,
          town,
          region,
          description,
          images // Array of base64 encoded images
        } = body;

        // Validate required fields
        if (!userId || !itemName || !category || !price || !town) {
          return {
            statusCode: 400,
            headers: {
              "Access-Control-Allow-Origin": "*",
              "Access-Control-Allow-Headers": "Content-Type",
              "Access-Control-Allow-Methods": "POST, OPTIONS"
            },
            body: JSON.stringify({
              error: "Missing required fields: userId, itemName, category, price, town"
            })
          };
        }

        // Generate unique item ID
        const itemId = crypto.randomUUID();
        const timestamp = new Date().toISOString();

        // Upload images to S3 and get URLs
        const imageUrls = [];
        if (images && images.length > 0) {
          for (let i = 0; i < images.length; i++) {
            const imageData = images[i];
            const imageKey = "items/" + itemId + "/image_" + (i + 1) + ".jpg";

            // Convert base64 to buffer
            const buffer = Buffer.from(imageData, 'base64');

            const uploadParams = {
              Bucket: s3BucketName,
              Key: imageKey,
              Body: buffer,
              ContentType: 'image/jpeg',
              ContentEncoding: 'base64'
            };

            try {
              await s3Client.send(new PutObjectCommand(uploadParams));
              const imageUrl = "https://" + s3BucketName + ".s3.amazonaws.com/" + imageKey;
              imageUrls.push(imageUrl);
              console.log("Image " + (i + 1) + " uploaded successfully:", imageUrl);
            } catch (uploadError) {
              console.error("Error uploading image " + (i + 1) + ":", uploadError);
              // Continue with other images even if one fails
            }
          }
        }

        // Create item object for DynamoDB
        const item = {
          itemId: itemId,
          userId: userId,
          itemName: itemName,
          category: category,
          price: parseFloat(price),
          "town/village": town,
          description: description || "",
          pictures: imageUrls,
          itemVisibility: "true", // Set visibility to true/on as requested
          region: region,
          createdAt: timestamp,
          updatedAt: timestamp
        };

        // Save item to DynamoDB
        const putParams = {
          TableName: itemsTableName,
          Item: item
        };

        console.log("Saving item to DynamoDB:", JSON.stringify(putParams, null, 2));
        await docClient.send(new PutCommand(putParams));

        console.log("Item saved successfully with ID:", itemId);

        // Return the created item
        return {
          statusCode: 201,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type",
            "Access-Control-Allow-Methods": "POST, OPTIONS"
          },
          body: JSON.stringify({
            success: true,
            item: item,
            message: "Item added successfully"
          })
        };

      } catch (error) {
        console.error("Error in add-item lambda:", error);
        return {
          statusCode: 500,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type",
            "Access-Control-Allow-Methods": "POST, OPTIONS"
          },
          body: JSON.stringify({
            error: "Internal server error",
            details: error.message
          })
        };
      }
    };
    EOF
    filename = "index.js"
  }
}

# Create the Lambda function for adding items
resource "aws_lambda_function" "add_item_lambda" {
  function_name    = "${var.project_name}-add-item-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.add_item_lambda.output_path
  source_code_hash = data.archive_file.add_item_lambda.output_base64sha256
  timeout          = 30 # Increase timeout for image uploads

  environment {
    variables = {
      ITEMS_TABLE_NAME     = var.items_table_name
      CATEGORIES_TABLE_NAME = var.categories_table_name
      S3_BUCKET_NAME       = var.s3_bucket_name
      ENVIRONMENT          = var.environment
    }
  }
}

# --- Get Stall Data Lambda ---

# Create a zip file for the get-stall-data function
data "archive_file" "get_stall_data_lambda" {
  type        = "zip"
  output_path = "${path.module}/get_stall_data_function.zip"
  source {
    content  = <<-EOF
    const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
    const { DynamoDBDocumentClient, GetCommand, QueryCommand } = require("@aws-sdk/lib-dynamodb");

    const client = new DynamoDBClient({});
    const docClient = DynamoDBDocumentClient.from(client);
    const usersTableName = process.env.USERS_TABLE_NAME;
    const sessionsTableName = process.env.SESSIONS_TABLE_NAME;
    const itemsTableName = process.env.ITEMS_TABLE_NAME;
    const itemsUserIdIndexName = process.env.ITEMS_USER_ID_INDEX_NAME;

    exports.handler = async (event) => {
      console.log("[DEBUG] Get stall data Lambda triggered with event:", JSON.stringify(event, null, 2));

      const headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type,Authorization,X-Session-Token",
        "Access-Control-Allow-Methods": "OPTIONS,GET"
      };

      // Handle CORS preflight requests
      if (event.httpMethod === 'OPTIONS') {
        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({ message: 'CORS preflight successful' })
        };
      }

      if (event.httpMethod !== 'GET') {
        return {
          statusCode: 405,
          headers: headers,
          body: JSON.stringify({ message: 'Method Not Allowed' }),
        };
      }

      try {
        // Get session token from headers
        const sessionToken = event.headers['X-Session-Token'] || event.headers['x-session-token'];

        if (!sessionToken) {
          return {
            statusCode: 401,
            headers: headers,
            body: JSON.stringify({
              success: false,
              message: "Missing session token in headers"
            }),
          };
        }

        console.log("Validating session token:", sessionToken);

        // Validate session
        const sessionParams = {
          TableName: sessionsTableName,
          Key: {
            sessionToken: sessionToken
          }
        };

        const sessionResult = await docClient.send(new GetCommand(sessionParams));
        const session = sessionResult.Item;

        if (!session) {
          return {
            statusCode: 401,
            headers: headers,
            body: JSON.stringify({
              success: false,
              message: "Invalid session token"
            }),
          };
        }

        // Check if session has expired
        const now = Math.floor(Date.now() / 1000);
        if (session.expiresAt < now) {
          return {
            statusCode: 401,
            headers: headers,
            body: JSON.stringify({
              success: false,
              message: "Session has expired"
            }),
          };
        }

        const userId = session.userId;
        console.log("Getting stall data for user:", userId);

        // Get user details
        const userParams = {
          TableName: usersTableName,
          Key: {
            userId: userId
          }
        };

        const userResult = await docClient.send(new GetCommand(userParams));
        const user = userResult.Item;

        if (!user) {
          return {
            statusCode: 404,
            headers: headers,
            body: JSON.stringify({
              success: false,
              message: "User not found"
            }),
          };
        }

        // Get user's items using GSI
        const itemsParams = {
          TableName: itemsTableName,
          IndexName: itemsUserIdIndexName,
          KeyConditionExpression: "userId = :userId",
          ExpressionAttributeValues: {
            ":userId": userId
          }
        };

        console.log("Querying items with params:", JSON.stringify(itemsParams, null, 2));
        const itemsResult = await docClient.send(new QueryCommand(itemsParams));
        const items = itemsResult.Items || [];

        console.log("Found", items.length, "items for user");

        // Prepare response data
        const stallData = {
          userInfo: {
            userId: user.userId,
            name: user.name,
            stallName: user.stallName,
            phoneNumber: user.phoneNumber,
            days: user.days || 0,
            profilePicture: user.profilePicture || null
          },
          items: items
        };

        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({
            success: true,
            stallData: stallData
          }),
        };

      } catch (error) {
        console.error("Error getting stall data:", error);
        return {
          statusCode: 500,
          headers: headers,
          body: JSON.stringify({
            success: false,
            message: "Error getting stall data",
            error: error.message
          }),
        };
      }
    };
    EOF
    filename = "index.js"
  }
}

# Create the Lambda function for getting stall data
resource "aws_lambda_function" "get_stall_data_lambda" {
  function_name    = "${var.project_name}-get-stall-data-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.get_stall_data_lambda.output_path
  source_code_hash = data.archive_file.get_stall_data_lambda.output_base64sha256
  timeout          = 15 # Increased timeout for querying multiple tables

  environment {
    variables = {
      USERS_TABLE_NAME           = var.users_table_name
      SESSIONS_TABLE_NAME        = var.sessions_table_name
      ITEMS_TABLE_NAME           = var.items_table_name
      ITEMS_USER_ID_INDEX_NAME   = var.items_user_id_index_name
      ENVIRONMENT                = var.environment
    }
  }
}

# --- Update User Settings Lambda ---

# Create a zip file for the update-user-settings function
data "archive_file" "update_user_settings_lambda" {
  type        = "zip"
  output_path = "${path.module}/update_user_settings_function.zip"
  source {
    content  = <<-EOF
    const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
    const { DynamoDBDocumentClient, UpdateCommand } = require("@aws-sdk/lib-dynamodb");
    const { S3Client, PutObjectCommand } = require("@aws-sdk/client-s3");
    const crypto = require('crypto');

    const dbClient = new DynamoDBClient({});
    const docClient = DynamoDBDocumentClient.from(dbClient);
    const s3Client = new S3Client({});

    const usersTableName = process.env.USERS_TABLE_NAME;
    const s3BucketName = process.env.S3_BUCKET_NAME;

    exports.handler = async (event) => {
      console.log("Update User Settings Lambda triggered with event:", JSON.stringify(event, null, 2));

      const headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      };

      // Handle CORS preflight requests
      if (event.httpMethod === 'OPTIONS') {
        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({ message: 'CORS preflight successful' })
        };
      }

      if (event.httpMethod !== 'POST') {
        return {
          statusCode: 405,
          headers: headers,
          body: JSON.stringify({ message: 'Method Not Allowed' }),
        };
      }

      try {
        // Parse the request body
        const body = JSON.parse(event.body);
        const {
          userId,
          name,
          stallName,
          profilePicture // Base64 encoded image or null
        } = body;

        // Validate required fields
        if (!userId) {
          return {
            statusCode: 400,
            headers: headers,
            body: JSON.stringify({
              error: "Missing required field: userId"
            })
          };
        }

        const timestamp = new Date().toISOString();
        let profilePictureUrl = null;

        // Upload profile picture to S3 if provided
        if (profilePicture) {
          const imageKey = "profile-pictures/" + userId + "/profile.jpg";

          // Convert base64 to buffer
          const buffer = Buffer.from(profilePicture, 'base64');

          const uploadParams = {
            Bucket: s3BucketName,
            Key: imageKey,
            Body: buffer,
            ContentType: 'image/jpeg',
            ContentEncoding: 'base64'
          };

          try {
            await s3Client.send(new PutObjectCommand(uploadParams));
            profilePictureUrl = "https://" + s3BucketName + ".s3.amazonaws.com/" + imageKey;
            console.log("Profile picture uploaded successfully:", profilePictureUrl);
          } catch (uploadError) {
            console.error("Error uploading profile picture:", uploadError);
            return {
              statusCode: 500,
              headers: headers,
              body: JSON.stringify({
                error: "Failed to upload profile picture",
                details: uploadError.message
              })
            };
          }
        }

        // Prepare update expression and attribute values
        let updateExpression = "SET updatedAt = :updatedAt";
        const expressionAttributeValues = {
          ":updatedAt": timestamp
        };

        // Add fields to update if they are provided
        if (name !== undefined) {
          updateExpression += ", #name = :name";
          expressionAttributeValues[":name"] = name;
        }

        if (stallName !== undefined) {
          updateExpression += ", stallName = :stallName";
          expressionAttributeValues[":stallName"] = stallName;
        }

        if (profilePictureUrl) {
          updateExpression += ", profilePicture = :profilePicture";
          expressionAttributeValues[":profilePicture"] = profilePictureUrl;
        }

        // Update user in DynamoDB
        const updateParams = {
          TableName: usersTableName,
          Key: {
            userId: userId
          },
          UpdateExpression: updateExpression,
          ExpressionAttributeValues: expressionAttributeValues,
          ...(name !== undefined && {
            ExpressionAttributeNames: {
              "#name": "name"
            }
          }),
          ReturnValues: "ALL_NEW"
        };

        console.log("Updating user in DynamoDB:", JSON.stringify(updateParams, null, 2));
        const result = await docClient.send(new UpdateCommand(updateParams));

        console.log("User updated successfully:", result.Attributes);

        // Return the updated user data
        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({
            success: true,
            user: result.Attributes,
            message: "User settings updated successfully"
          })
        };

      } catch (error) {
        console.error("Error in update-user-settings lambda:", error);
        return {
          statusCode: 500,
          headers: headers,
          body: JSON.stringify({
            error: "Internal server error",
            details: error.message
          })
        };
      }
    };
    EOF
    filename = "index.js"
  }
}

# Create the Lambda function for updating user settings
resource "aws_lambda_function" "update_user_settings_lambda" {
  function_name    = "${var.project_name}-update-user-settings-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.update_user_settings_lambda.output_path
  source_code_hash = data.archive_file.update_user_settings_lambda.output_base64sha256
  timeout          = 30 # Increase timeout for image uploads

  environment {
    variables = {
      USERS_TABLE_NAME = var.users_table_name
      S3_BUCKET_NAME   = var.s3_bucket_name
      ENVIRONMENT      = var.environment
    }
  }
}

# --- API Gateway Integrations ---

# Integration for check-phone POST method
resource "aws_api_gateway_integration" "check_phone_post" {
  rest_api_id = var.api_gateway_id
  resource_id = var.check_phone_resource_id
  http_method = "POST"

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = aws_lambda_function.check_phone_lambda.invoke_arn
}

# Integration for send-code POST method
resource "aws_api_gateway_integration" "send_code_post" {
  rest_api_id = var.api_gateway_id
  resource_id = var.send_code_resource_id
  http_method = "POST"

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = aws_lambda_function.send_code_lambda.invoke_arn
}

# Integration for verify-code POST method
resource "aws_api_gateway_integration" "verify_code_post" {
  rest_api_id = var.api_gateway_id
  resource_id = var.verify_code_resource_id
  http_method = "POST"

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = aws_lambda_function.verify_code_lambda.invoke_arn
}

# Integration for register-user POST method
resource "aws_api_gateway_integration" "register_user_post" {
  rest_api_id = var.api_gateway_id
  resource_id = var.register_user_resource_id
  http_method = "POST"

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = aws_lambda_function.register_user_lambda.invoke_arn
}

# Integration for check-session POST method
resource "aws_api_gateway_integration" "check_session_post" {
  rest_api_id = var.api_gateway_id
  resource_id = var.check_session_resource_id
  http_method = "POST"

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = aws_lambda_function.check_session_lambda.invoke_arn
}

# Integration for get-stall-data GET method
resource "aws_api_gateway_integration" "get_stall_data_get" {
  rest_api_id = var.api_gateway_id
  resource_id = var.get_stall_data_resource_id
  http_method = "GET"

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = aws_lambda_function.get_stall_data_lambda.invoke_arn
}

# Integration for add-item POST method
resource "aws_api_gateway_integration" "add_item_post" {
  rest_api_id = var.api_gateway_id
  resource_id = var.add_item_resource_id
  http_method = "POST"

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = aws_lambda_function.add_item_lambda.invoke_arn
}

# Integration for update-user-settings POST method
resource "aws_api_gateway_integration" "update_user_settings_post" {
  rest_api_id = var.api_gateway_id
  resource_id = var.update_user_settings_resource_id
  http_method = "POST"

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = aws_lambda_function.update_user_settings_lambda.invoke_arn
}

# API Gateway integration for remove-profile-picture
resource "aws_api_gateway_integration" "remove_profile_picture_post" {
  rest_api_id = var.api_gateway_id
  resource_id = var.remove_profile_picture_resource_id
  http_method = "POST"

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = aws_lambda_function.remove_profile_picture_lambda.invoke_arn
}

# Method responses for POST methods
resource "aws_api_gateway_method_response" "check_phone_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.check_phone_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }
}

resource "aws_api_gateway_method_response" "send_code_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.send_code_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }
}

resource "aws_api_gateway_method_response" "verify_code_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.verify_code_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }
}

resource "aws_api_gateway_method_response" "register_user_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.register_user_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }
}

resource "aws_api_gateway_method_response" "check_session_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.check_session_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }
}

# Method response for GET
resource "aws_api_gateway_method_response" "get_stall_data_get_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.get_stall_data_resource_id
  http_method = "GET"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }
}

# Method response for add-item POST
resource "aws_api_gateway_method_response" "add_item_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.add_item_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }
}

# Method response for update-user-settings POST
resource "aws_api_gateway_method_response" "update_user_settings_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.update_user_settings_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }
}

# Method response for remove-profile-picture POST
resource "aws_api_gateway_method_response" "remove_profile_picture_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.remove_profile_picture_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }
}

# Integration responses for POST methods
resource "aws_api_gateway_integration_response" "check_phone_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.check_phone_resource_id
  http_method = "POST"
  status_code = aws_api_gateway_method_response.check_phone_post_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }

  depends_on = [aws_api_gateway_integration.check_phone_post]
}

resource "aws_api_gateway_integration_response" "send_code_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.send_code_resource_id
  http_method = "POST"
  status_code = aws_api_gateway_method_response.send_code_post_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }

  depends_on = [aws_api_gateway_integration.send_code_post]
}

resource "aws_api_gateway_integration_response" "verify_code_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.verify_code_resource_id
  http_method = "POST"
  status_code = aws_api_gateway_method_response.verify_code_post_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }

  depends_on = [aws_api_gateway_integration.verify_code_post]
}

resource "aws_api_gateway_integration_response" "register_user_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.register_user_resource_id
  http_method = "POST"
  status_code = aws_api_gateway_method_response.register_user_post_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }

  depends_on = [aws_api_gateway_integration.register_user_post]
}

resource "aws_api_gateway_integration_response" "check_session_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.check_session_resource_id
  http_method = "POST"
  status_code = aws_api_gateway_method_response.check_session_post_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }

  depends_on = [aws_api_gateway_integration.check_session_post]
}

# Integration response for GET
resource "aws_api_gateway_integration_response" "get_stall_data_get_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.get_stall_data_resource_id
  http_method = "GET"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }

  depends_on = [aws_api_gateway_integration.get_stall_data_get]
}

# Integration response for add-item POST
resource "aws_api_gateway_integration_response" "add_item_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.add_item_resource_id
  http_method = "POST"
  status_code = aws_api_gateway_method_response.add_item_post_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }

  depends_on = [aws_api_gateway_integration.add_item_post]
}

# Integration response for update-user-settings POST
resource "aws_api_gateway_integration_response" "update_user_settings_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.update_user_settings_resource_id
  http_method = "POST"
  status_code = aws_api_gateway_method_response.update_user_settings_post_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }

  depends_on = [aws_api_gateway_integration.update_user_settings_post]
}

# Integration response for remove-profile-picture POST
resource "aws_api_gateway_integration_response" "remove_profile_picture_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.remove_profile_picture_resource_id
  http_method = "POST"
  status_code = aws_api_gateway_method_response.remove_profile_picture_post_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }

  depends_on = [aws_api_gateway_integration.remove_profile_picture_post]
}

# Note: API Gateway deployment is managed in the API Gateway module

# --- API Gateway Integration ---
# Note: API Gateway resources (endpoints) already exist and are managed elsewhere
# The endpoints are working and accessible at:
# - /check-phone
# - /send-code
# - /verify-code
# - /register-user
# - /check-session

# --- Lambda Permissions for API Gateway ---
# These permissions allow the existing API Gateway endpoints to invoke the Lambda functions

resource "aws_lambda_permission" "api_gateway_check_phone_permission" {
  statement_id  = "AllowAPIGatewayInvokeCheckPhone"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.check_phone_lambda.function_name
  principal     = "apigateway.amazonaws.com"

  # Specific source ARN for check-phone endpoint with explicit stage name
  source_arn    = "${var.api_gateway_execution_arn}/dev/POST/check-phone"
}

resource "aws_lambda_permission" "api_gateway_send_code_permission" {
  statement_id  = "AllowAPIGatewayInvokeSendCode"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.send_code_lambda.function_name
  principal     = "apigateway.amazonaws.com"

  # Specific source ARN for send-code endpoint with explicit stage name
  source_arn    = "${var.api_gateway_execution_arn}/dev/POST/send-code"
}

resource "aws_lambda_permission" "api_gateway_verify_code_permission" {
  statement_id  = "AllowAPIGatewayInvokeVerifyCode"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.verify_code_lambda.function_name
  principal     = "apigateway.amazonaws.com"

  # Specific source ARN for verify-code endpoint with explicit stage name
  source_arn    = "${var.api_gateway_execution_arn}/dev/POST/verify-code"
}

resource "aws_lambda_permission" "api_gateway_register_user_permission" {
  statement_id  = "AllowAPIGatewayInvokeRegisterUser"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.register_user_lambda.function_name
  principal     = "apigateway.amazonaws.com"

  # Specific source ARN for register-user endpoint with explicit stage name
  source_arn    = "${var.api_gateway_execution_arn}/dev/POST/register-user"
}

resource "aws_lambda_permission" "api_gateway_check_session_permission" {
  statement_id  = "AllowAPIGatewayInvokeCheckSession"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.check_session_lambda.function_name
  principal     = "apigateway.amazonaws.com"

  # Specific source ARN for check-session endpoint with explicit stage name
  source_arn    = "${var.api_gateway_execution_arn}/dev/POST/check-session"
}

resource "aws_lambda_permission" "api_gateway_get_stall_data_permission" {
  statement_id  = "AllowAPIGatewayInvokeGetStallData"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.get_stall_data_lambda.function_name
  principal     = "apigateway.amazonaws.com"

  # Specific source ARN for get-stall-data endpoint with explicit stage name
  source_arn    = "${var.api_gateway_execution_arn}/dev/GET/get-stall-data"
}

resource "aws_lambda_permission" "api_gateway_add_item_permission" {
  statement_id  = "AllowAPIGatewayInvokeAddItem"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.add_item_lambda.function_name
  principal     = "apigateway.amazonaws.com"

  # Specific source ARN for add-item endpoint with explicit stage name
  source_arn    = "${var.api_gateway_execution_arn}/dev/POST/add-item"
}

resource "aws_lambda_permission" "api_gateway_update_user_settings_permission" {
  statement_id  = "AllowAPIGatewayInvokeUpdateUserSettings"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.update_user_settings_lambda.function_name
  principal     = "apigateway.amazonaws.com"

  # Specific source ARN for update-user-settings endpoint with explicit stage name
  source_arn    = "${var.api_gateway_execution_arn}/dev/POST/update-user-settings"
}

resource "aws_lambda_permission" "api_gateway_remove_profile_picture_permission" {
  statement_id  = "AllowAPIGatewayInvokeRemoveProfilePicture"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.remove_profile_picture_lambda.function_name
  principal     = "apigateway.amazonaws.com"

  # Specific source ARN for remove-profile-picture endpoint with explicit stage name
  source_arn    = "${var.api_gateway_execution_arn}/dev/POST/remove-profile-picture"
}

# --- Remove Profile Picture Lambda ---

# Create a zip file for the remove-profile-picture function
data "archive_file" "remove_profile_picture_lambda" {
  type        = "zip"
  output_path = "${path.module}/remove_profile_picture_function.zip"
  source {
    content  = <<-EOF
    const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
    const { DynamoDBDocumentClient, UpdateCommand, GetCommand } = require("@aws-sdk/lib-dynamodb");
    const { S3Client, DeleteObjectCommand } = require("@aws-sdk/client-s3");

    const dbClient = new DynamoDBClient({});
    const docClient = DynamoDBDocumentClient.from(dbClient);
    const s3Client = new S3Client({});

    const usersTableName = process.env.USERS_TABLE_NAME;
    const s3BucketName = process.env.S3_BUCKET_NAME;

    exports.handler = async (event) => {
      console.log("Remove Profile Picture Lambda triggered with event:", JSON.stringify(event, null, 2));

      const headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      };

      // Handle CORS preflight requests
      if (event.httpMethod === 'OPTIONS') {
        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({ message: 'CORS preflight successful' })
        };
      }

      if (event.httpMethod !== 'POST') {
        return {
          statusCode: 405,
          headers: headers,
          body: JSON.stringify({ message: 'Method Not Allowed' }),
        };
      }

      try {
        // Parse the request body
        const body = JSON.parse(event.body);
        const { userId } = body;

        // Validate required fields
        if (!userId) {
          return {
            statusCode: 400,
            headers: headers,
            body: JSON.stringify({
              error: "Missing required field: userId"
            })
          };
        }

        // Get current user data to find existing profile picture
        const getUserParams = {
          TableName: usersTableName,
          Key: {
            userId: userId
          }
        };

        const userResult = await docClient.send(new GetCommand(getUserParams));

        if (!userResult.Item) {
          return {
            statusCode: 404,
            headers: headers,
            body: JSON.stringify({
              error: "User not found"
            })
          };
        }

        const user = userResult.Item;

        // If user has a profile picture, delete it from S3
        if (user.profilePicture) {
          try {
            // Extract the S3 key from the URL
            const s3Key = "profile-pictures/" + userId + "/profile.jpg";

            const deleteParams = {
              Bucket: s3BucketName,
              Key: s3Key
            };

            await s3Client.send(new DeleteObjectCommand(deleteParams));
            console.log("Profile picture deleted from S3:", s3Key);
          } catch (s3Error) {
            console.error("Error deleting from S3:", s3Error);
            // Continue with database update even if S3 deletion fails
          }
        }

        // Update user record to remove profile picture
        const updateParams = {
          TableName: usersTableName,
          Key: {
            userId: userId
          },
          UpdateExpression: "REMOVE profilePicture SET updatedAt = :updatedAt",
          ExpressionAttributeValues: {
            ":updatedAt": new Date().toISOString()
          },
          ReturnValues: "ALL_NEW"
        };

        const result = await docClient.send(new UpdateCommand(updateParams));

        console.log("Profile picture removed successfully for user:", userId);

        return {
          statusCode: 200,
          headers: headers,
          body: JSON.stringify({
            success: true,
            message: "Profile picture removed successfully",
            user: result.Attributes
          })
        };

      } catch (error) {
        console.error("Error in remove-profile-picture lambda:", error);
        return {
          statusCode: 500,
          headers: headers,
          body: JSON.stringify({
            error: "Internal server error",
            details: error.message
          })
        };
      }
    };
    EOF
    filename = "index.js"
  }
}

# Create the Lambda function for removing profile picture
resource "aws_lambda_function" "remove_profile_picture_lambda" {
  function_name    = "${var.project_name}-remove-profile-picture-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.remove_profile_picture_lambda.output_path
  source_code_hash = data.archive_file.remove_profile_picture_lambda.output_base64sha256
  timeout          = 30

  environment {
    variables = {
      USERS_TABLE_NAME = var.users_table_name
      S3_BUCKET_NAME   = var.s3_bucket_name
      ENVIRONMENT      = var.environment
    }
  }
}

# --- Outputs ---
output "check_phone_lambda_arn" {
  value = aws_lambda_function.check_phone_lambda.arn
}

output "send_code_lambda_arn" {
  value = aws_lambda_function.send_code_lambda.arn
}

output "verify_code_lambda_arn" {
  value = aws_lambda_function.verify_code_lambda.arn
}

output "register_user_lambda_arn" {
  value = aws_lambda_function.register_user_lambda.arn
}

output "check_session_lambda_arn" {
  value = aws_lambda_function.check_session_lambda.arn
}

output "get_stall_data_lambda_arn" {
  value = aws_lambda_function.get_stall_data_lambda.arn
}

output "get_stall_data_lambda_invoke_arn" {
  value = aws_lambda_function.get_stall_data_lambda.invoke_arn
}

output "add_item_lambda_arn" {
  value = aws_lambda_function.add_item_lambda.arn
}

# --- Update Item Lambda ---

# Create a zip file for the update-item function
data "archive_file" "update_item_lambda" {
  type        = "zip"
  output_path = "${path.module}/update_item_function.zip"
  source {
    content = file("${path.root}/update_item_lambda.js")
    filename = "update_item_lambda.js"
  }
}

# Create the Lambda function for updating items
resource "aws_lambda_function" "update_item_lambda" {
  function_name    = "${var.project_name}-update-item-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "update_item_lambda.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.update_item_lambda.output_path
  source_code_hash = data.archive_file.update_item_lambda.output_base64sha256
  timeout          = 30

  environment {
    variables = {
      ITEMS_TABLE_NAME = var.items_table_name
      S3_BUCKET_NAME   = var.s3_bucket_name
      ENVIRONMENT      = var.environment
    }
  }
}

output "add_item_lambda_invoke_arn" {
  value = aws_lambda_function.add_item_lambda.invoke_arn
}

output "update_item_lambda_arn" {
  value = aws_lambda_function.update_item_lambda.arn
}

output "update_item_lambda_invoke_arn" {
  value = aws_lambda_function.update_item_lambda.invoke_arn
}

# --- Migrate Items Lambda ---

# Create a zip file for the migrate-items function
data "archive_file" "migrate_items_lambda" {
  type        = "zip"
  output_path = "${path.module}/migrate_items_function.zip"
  source {
    content = file("${path.root}/migrate_items_lambda.js")
    filename = "migrate_items_lambda.js"
  }
}

# Create the Lambda function for migrating items data
resource "aws_lambda_function" "migrate_items_lambda" {
  function_name    = "${var.project_name}-migrate-items-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "migrate_items_lambda.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.migrate_items_lambda.output_path
  source_code_hash = data.archive_file.migrate_items_lambda.output_base64sha256
  timeout          = 300 # 5 minutes for data migration

  environment {
    variables = {
      ITEMS_TABLE_NAME = var.items_table_name
      ENVIRONMENT      = var.environment
    }
  }
}

output "migrate_items_lambda_arn" {
  value = aws_lambda_function.migrate_items_lambda.arn
}

# --- Guest Register Lambda ---

# Archive file for guest register Lambda function
data "archive_file" "guest_register_lambda" {
  type        = "zip"
  output_path = "modules/lambda/guest_register.zip"

  source {
    content = <<EOT
const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
const { DynamoDBDocumentClient, PutCommand } = require("@aws-sdk/lib-dynamodb");
const { randomUUID } = require('crypto');

const client = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(client);
const guestsTableName = process.env.GUESTS_TABLE_NAME;

exports.handler = async (event) => {
  console.log("Guest register Lambda triggered with event:", JSON.stringify(event, null, 2));

  const headers = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers": "Content-Type",
    "Access-Control-Allow-Methods": "POST, OPTIONS"
  };

  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: headers,
      body: JSON.stringify({ message: 'CORS preflight successful' })
    };
  }

  try {
    // Generate a new guest ID
    const guestId = randomUUID();
    const now = Math.floor(Date.now() / 1000);
    const expiresAt = now + (20 * 24 * 60 * 60); // 20 days from now

    // Save guest to DynamoDB
    const putParams = {
      TableName: guestsTableName,
      Item: {
        guestId: guestId,
        createdAt: now,
        lastSeenAt: now,
        expiresAt: expiresAt
      }
    };

    await docClient.send(new PutCommand(putParams));

    console.log("Guest registered successfully:", guestId);

    return {
      statusCode: 200,
      headers: headers,
      body: JSON.stringify({
        success: true,
        guestId: guestId,
        message: "Guest registered successfully"
      })
    };
  } catch (error) {
    console.error("Error registering guest:", error);
    return {
      statusCode: 500,
      headers: headers,
      body: JSON.stringify({
        success: false,
        error: "Internal server error",
        details: error.message
      })
    };
  }
};
EOT
    filename = "index.js"
  }
}

# Create the Lambda function for guest register
resource "aws_lambda_function" "guest_register_lambda" {
  function_name    = "${var.project_name}-guest-register-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.guest_register_lambda.output_path
  source_code_hash = data.archive_file.guest_register_lambda.output_base64sha256
  timeout          = 30

  environment {
    variables = {
      GUESTS_TABLE_NAME = var.guests_table_name
      ENVIRONMENT       = var.environment
    }
  }
}

# --- Guest Verify Lambda ---

# Archive file for guest verify Lambda function
data "archive_file" "guest_verify_lambda" {
  type        = "zip"
  output_path = "modules/lambda/guest_verify.zip"

  source {
    content = <<EOT
const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
const { DynamoDBDocumentClient, GetCommand, PutCommand, UpdateCommand } = require("@aws-sdk/lib-dynamodb");
const { randomUUID } = require('crypto');

const client = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(client);
const guestsTableName = process.env.GUESTS_TABLE_NAME;

exports.handler = async (event) => {
  console.log("Guest verify Lambda triggered with event:", JSON.stringify(event, null, 2));

  const headers = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers": "Content-Type",
    "Access-Control-Allow-Methods": "POST, OPTIONS"
  };

  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: headers,
      body: JSON.stringify({ message: 'CORS preflight successful' })
    };
  }

  try {
    const body = JSON.parse(event.body);
    const { guestId } = body;

    if (!guestId) {
      return {
        statusCode: 400,
        headers: headers,
        body: JSON.stringify({
          success: false,
          error: "Guest ID is required"
        })
      };
    }

    // Check if guest exists in database
    const getParams = {
      TableName: guestsTableName,
      Key: {
        guestId: guestId
      }
    };

    const result = await docClient.send(new GetCommand(getParams));

    if (result.Item) {
      // Guest exists, update lastSeenAt
      const now = Math.floor(Date.now() / 1000);

      const updateParams = {
        TableName: guestsTableName,
        Key: {
          guestId: guestId
        },
        UpdateExpression: "SET lastSeenAt = :lastSeenAt",
        ExpressionAttributeValues: {
          ":lastSeenAt": now
        }
      };

      await docClient.send(new UpdateCommand(updateParams));

      console.log("Guest verified and updated:", guestId);

      return {
        statusCode: 200,
        headers: headers,
        body: JSON.stringify({
          success: true,
          guestId: guestId,
          message: "Guest verified successfully"
        })
      };
    } else {
      // Guest doesn't exist, create a new one
      const newGuestId = randomUUID();
      const now = Math.floor(Date.now() / 1000);
      const expiresAt = now + (20 * 24 * 60 * 60); // 20 days from now

      const putParams = {
        TableName: guestsTableName,
        Item: {
          guestId: newGuestId,
          createdAt: now,
          lastSeenAt: now,
          expiresAt: expiresAt
        }
      };

      await docClient.send(new PutCommand(putParams));

      console.log("New guest created:", newGuestId);

      return {
        statusCode: 200,
        headers: headers,
        body: JSON.stringify({
          success: true,
          guestId: newGuestId,
          message: "New guest created successfully"
        })
      };
    }
  } catch (error) {
    console.error("Error verifying guest:", error);
    return {
      statusCode: 500,
      headers: headers,
      body: JSON.stringify({
        success: false,
        error: "Internal server error",
        details: error.message
      })
    };
  }
};
EOT
    filename = "index.js"
  }
}

# Create the Lambda function for guest verify
resource "aws_lambda_function" "guest_verify_lambda" {
  function_name    = "${var.project_name}-guest-verify-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.guest_verify_lambda.output_path
  source_code_hash = data.archive_file.guest_verify_lambda.output_base64sha256
  timeout          = 30

  environment {
    variables = {
      GUESTS_TABLE_NAME = var.guests_table_name
      ENVIRONMENT       = var.environment
    }
  }
}

# --- Guest Verify Splash Lambda ---

# Archive file for guest verify splash Lambda function
data "archive_file" "guest_verify_splash_lambda" {
  type        = "zip"
  output_path = "modules/lambda/guest_verify_splash.zip"

  source {
    content = <<EOT
const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
const { DynamoDBDocumentClient, GetCommand } = require("@aws-sdk/lib-dynamodb");

const client = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(client);
const guestsTableName = process.env.GUESTS_TABLE_NAME;

exports.handler = async (event) => {
  console.log("Guest verify splash Lambda triggered with event:", JSON.stringify(event, null, 2));

  const headers = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers": "Content-Type",
    "Access-Control-Allow-Methods": "POST, OPTIONS"
  };

  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: headers,
      body: JSON.stringify({ message: 'CORS preflight successful' })
    };
  }

  try {
    const body = JSON.parse(event.body);
    const { guestId } = body;

    if (!guestId) {
      return {
        statusCode: 400,
        headers: headers,
        body: JSON.stringify({
          success: false,
          exists: false,
          error: "Guest ID is required"
        })
      };
    }

    // Check if guest exists in database (simple existence check only)
    const getParams = {
      TableName: guestsTableName,
      Key: {
        guestId: guestId
      }
    };

    const result = await docClient.send(new GetCommand(getParams));

    const exists = !!result.Item;
    console.log("Guest existence check result:", { guestId, exists });

    return {
      statusCode: 200,
      headers: headers,
      body: JSON.stringify({
        success: true,
        exists: exists,
        message: exists ? "Guest exists" : "Guest not found"
      })
    };
  } catch (error) {
    console.error("Error checking guest existence:", error);
    return {
      statusCode: 500,
      headers: headers,
      body: JSON.stringify({
        success: false,
        exists: false,
        error: "Internal server error",
        details: error.message
      })
    };
  }
};
EOT
    filename = "index.js"
  }
}

# Create the Lambda function for guest verify splash
resource "aws_lambda_function" "guest_verify_splash_lambda" {
  function_name    = "${var.project_name}-guest-verify-splash-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.guest_verify_splash_lambda.output_path
  source_code_hash = data.archive_file.guest_verify_splash_lambda.output_base64sha256
  timeout          = 30

  environment {
    variables = {
      GUESTS_TABLE_NAME = var.guests_table_name
      ENVIRONMENT       = var.environment
    }
  }
}

# --- Guest Register API Gateway Integration ---

# API Gateway Integration for guest-register POST
resource "aws_api_gateway_integration" "guest_register_post" {
  rest_api_id             = var.api_gateway_id
  resource_id             = var.guest_register_resource_id
  http_method             = "POST"
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/${aws_lambda_function.guest_register_lambda.arn}/invocations"
}

# Method Response for guest-register POST
resource "aws_api_gateway_method_response" "guest_register_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.guest_register_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }
}

# Integration Response for guest-register POST
resource "aws_api_gateway_integration_response" "guest_register_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.guest_register_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }
}

# Lambda Permission for guest-register
resource "aws_lambda_permission" "api_gateway_guest_register_permission" {
  statement_id  = "AllowAPIGatewayInvokeGuestRegister"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.guest_register_lambda.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.region}:${var.account_id}:${var.api_gateway_id}/${var.environment}/POST/guest-register"
}

# --- Guest Verify API Gateway Integration ---

# API Gateway Integration for guest-verify POST
resource "aws_api_gateway_integration" "guest_verify_post" {
  rest_api_id             = var.api_gateway_id
  resource_id             = var.guest_verify_resource_id
  http_method             = "POST"
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/${aws_lambda_function.guest_verify_lambda.arn}/invocations"
}

# Method Response for guest-verify POST
resource "aws_api_gateway_method_response" "guest_verify_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.guest_verify_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }
}

# Integration Response for guest-verify POST
resource "aws_api_gateway_integration_response" "guest_verify_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.guest_verify_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }
}

# Lambda Permission for guest-verify
resource "aws_lambda_permission" "api_gateway_guest_verify_permission" {
  statement_id  = "AllowAPIGatewayInvokeGuestVerify"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.guest_verify_lambda.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.region}:${var.account_id}:${var.api_gateway_id}/${var.environment}/POST/guest-verify"
}

# --- Recycle Verification Code Lambda ---

# Create archive for recycle verification code Lambda
data "archive_file" "recycle_verification_code_lambda" {
  type        = "zip"
  output_path = "modules/lambda/recycle_verification_code_function.zip"

  source {
    content = <<EOT
const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
const { DynamoDBDocumentClient, GetCommand, PutCommand, DeleteCommand } = require("@aws-sdk/lib-dynamodb");

const client = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(client);
const codesTableName = process.env.VERIFICATION_CODES_TABLE_NAME;
const codesRecycleTableName = process.env.VERIFICATION_CODES_RECYCLE_TABLE_NAME;

exports.handler = async (event) => {
  console.log("Recycle verification code triggered with event:", JSON.stringify(event, null, 2));

  const headers = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers": "Content-Type",
    "Access-Control-Allow-Methods": "POST, OPTIONS"
  };

  // Handle CORS preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: headers,
      body: JSON.stringify({ message: 'CORS preflight successful' })
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers: headers,
      body: JSON.stringify({ message: 'Method Not Allowed' }),
    };
  }

  try {
    // Parse the request body
    const body = JSON.parse(event.body);
    const phoneNumber = body.phoneNumber;

    if (!phoneNumber) {
      return {
        statusCode: 400,
        headers: headers,
        body: JSON.stringify({
          success: false,
          message: "Phone number is required"
        })
      };
    }

    // Step 1: Check if user exists in verification-codes-recycle table
    const getRecycleParams = {
      TableName: codesRecycleTableName,
      Key: {
        phoneNumber: phoneNumber
      }
    };

    const recycleResult = await docClient.send(new GetCommand(getRecycleParams));
    const recycleRecord = recycleResult.Item;

    // If no record found, return success and allow normal logout to proceed
    if (!recycleRecord) {
      console.log("No recycle record found for phone:", phoneNumber);
      return {
        statusCode: 200,
        headers: headers,
        body: JSON.stringify({
          success: true,
          message: "No verification code to recycle"
        })
      };
    }

    // Step 2: Check expiration
    const now = Math.floor(Date.now() / 1000);
    if (recycleRecord.expiresAt <= now) {
      // Code is expired, delete from recycle table
      console.log("Recycle record expired, deleting:", phoneNumber);
      await docClient.send(new DeleteCommand(getRecycleParams));

      return {
        statusCode: 200,
        headers: headers,
        body: JSON.stringify({
          success: true,
          message: "Expired verification code removed"
        })
      };
    }

    // Step 3: Record exists and NOT expired - move to main verification table
    console.log("Moving unexpired code from recycle to main table:", phoneNumber);

    // Create the record in main verification codes table
    const putMainParams = {
      TableName: codesTableName,
      Item: {
        phoneNumber: recycleRecord.phoneNumber,
        code: recycleRecord.code,
        createdAt: recycleRecord.createdAt,
        expiresAt: recycleRecord.expiresAt
        // Note: We don't include verifiedAt in the main table
      }
    };

    await docClient.send(new PutCommand(putMainParams));

    // Delete from recycle table
    await docClient.send(new DeleteCommand(getRecycleParams));

    console.log("Successfully recycled verification code for:", phoneNumber);

    return {
      statusCode: 200,
      headers: headers,
      body: JSON.stringify({
        success: true,
        message: "Verification code recycled successfully"
      })
    };

  } catch (error) {
    console.error("Error recycling verification code:", error);
    // Always return success to ensure logout works
    return {
      statusCode: 200,
      headers: headers,
      body: JSON.stringify({
        success: true,
        message: "Logout proceeding (recycle failed safely)"
      })
    };
  }
};
EOT
    filename = "index.js"
  }
}

# Create the Lambda function for recycling verification codes
resource "aws_lambda_function" "recycle_verification_code_lambda" {
  function_name    = "${var.project_name}-recycle-verification-code-${var.environment}"
  role             = aws_iam_role.lambda_role.arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  filename         = data.archive_file.recycle_verification_code_lambda.output_path
  source_code_hash = data.archive_file.recycle_verification_code_lambda.output_base64sha256
  timeout          = 10

  environment {
    variables = {
      VERIFICATION_CODES_TABLE_NAME = var.verification_codes_table_name
      VERIFICATION_CODES_RECYCLE_TABLE_NAME = var.verification_codes_recycle_table_name
      ENVIRONMENT = var.environment
    }
  }
}



# Lambda permission for API Gateway to invoke recycle verification code function
resource "aws_lambda_permission" "api_gateway_recycle_verification_code_permission" {
  statement_id  = "AllowAPIGatewayInvokeRecycleVerificationCode"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.recycle_verification_code_lambda.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${var.api_gateway_execution_arn}/*/*"
}

# API Gateway integration for recycle verification code POST method
resource "aws_api_gateway_integration" "recycle_verification_code_post" {
  rest_api_id = var.api_gateway_rest_api_id
  resource_id = var.recycle_verification_code_resource_id
  http_method = "POST"
  type        = "AWS_PROXY"
  integration_http_method = "POST"
  uri         = aws_lambda_function.recycle_verification_code_lambda.invoke_arn
}

# Method response for recycle verification code POST
resource "aws_api_gateway_method_response" "recycle_verification_code_post_200" {
  rest_api_id = var.api_gateway_rest_api_id
  resource_id = var.recycle_verification_code_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }
}

# Integration response for recycle verification code POST
resource "aws_api_gateway_integration_response" "recycle_verification_code_post_200" {
  rest_api_id = var.api_gateway_rest_api_id
  resource_id = var.recycle_verification_code_resource_id
  http_method = aws_api_gateway_integration.recycle_verification_code_post.http_method
  status_code = aws_api_gateway_method_response.recycle_verification_code_post_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }

  depends_on = [aws_api_gateway_integration.recycle_verification_code_post]
}




