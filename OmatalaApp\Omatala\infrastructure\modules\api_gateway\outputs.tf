# API Gateway Outputs
output "api_gateway_id" {
  description = "The ID of the API Gateway"
  value       = aws_api_gateway_rest_api.api.id
}

output "root_resource_id" {
  description = "The root resource ID of the API Gateway"
  value       = aws_api_gateway_rest_api.api.root_resource_id
}

output "api_gateway_url" {
  description = "The base URL of the API Gateway stage"
  value       = "https://${aws_api_gateway_rest_api.api.id}.execute-api.${var.aws_region}.amazonaws.com/${var.environment}"
}

# Commented out since we're using custom auth
# output "cognito_authorizer_id" {
#   description = "The ID of the Cognito authorizer"
#   value       = aws_api_gateway_authorizer.cognito_authorizer.id
# }

output "api_gateway_execution_arn" {
  description = "The execution ARN of the API Gateway"
  value       = "${aws_api_gateway_rest_api.api.execution_arn}/*"
}

output "api_stage_arn" {
  description = "The ARN of the API Gateway stage"
  value       = aws_api_gateway_stage.api_stage.arn
}

output "api_stage_invoke_url" {
  description = "The URL to invoke the API Gateway stage"
  value       = aws_api_gateway_stage.api_stage.invoke_url
}

output "check_phone_resource_id" {
  description = "The resource ID for the check-phone endpoint"
  value       = aws_api_gateway_resource.check_phone.id
}

output "send_code_resource_id" {
  description = "The resource ID for the send-code endpoint"
  value       = aws_api_gateway_resource.send_code.id
}

output "verify_code_resource_id" {
  description = "The resource ID for the verify-code endpoint"
  value       = aws_api_gateway_resource.verify_code.id
}

output "check_pending_code_resource_id" {
  description = "The resource ID for the check-pending-code endpoint"
  value       = aws_api_gateway_resource.check_pending_code.id
}

output "register_user_resource_id" {
  description = "The resource ID for the register-user endpoint"
  value       = aws_api_gateway_resource.register_user.id
}

output "check_session_resource_id" {
  description = "The resource ID for the check-session endpoint"
  value       = aws_api_gateway_resource.check_session.id
}

output "get_stall_data_resource_id" {
  description = "The resource ID of the get-stall-data endpoint"
  value       = aws_api_gateway_resource.get_stall_data.id
}

output "add_item_resource_id" {
  description = "The resource ID of the add-item endpoint"
  value       = aws_api_gateway_resource.add_item.id
}

output "update_user_settings_resource_id" {
  description = "The resource ID of the update-user-settings endpoint"
  value       = aws_api_gateway_resource.update_user_settings.id
}

output "remove_profile_picture_resource_id" {
  description = "The resource ID of the remove-profile-picture endpoint"
  value       = aws_api_gateway_resource.remove_profile_picture.id
}

output "guest_register_resource_id" {
  description = "The resource ID of the guest-register endpoint"
  value       = aws_api_gateway_resource.guest_register.id
}

output "guest_verify_resource_id" {
  description = "The resource ID of the guest-verify endpoint"
  value       = aws_api_gateway_resource.guest_verify.id
}

output "recycle_verification_code_resource_id" {
  description = "The resource ID of the recycle-verification-code endpoint"
  value       = aws_api_gateway_resource.recycle_verification_code.id
}
