import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TextInput, ScrollView, TouchableOpacity, Image, Linking, Clipboard, Platform, SafeAreaView, Dimensions, Modal, TouchableWithoutFeedback, Animated, BackHandler } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import BottomNavigation from '../components/BottomNavigation';
import CategoriesScrollView from '../components/CategoriesScrollView';
import { omatalaFontStyle } from '../utils/fonts';
import { getCategoriesForFeed } from '../services/categoryService';
import { useExitAppOnBackButton } from '../utils/backButton';
import { useFocusEffect } from '@react-navigation/native';
import ProfileButton from '../components/ProfileButton';
import { clearSessionToken, getUserData } from '../backend/userService';
import { clearUserId } from '../utils/userIdStorage';
import awsApi from '../services/aws-api';

// Mock API functions
const mockAPI = {
  getProductData: () => {
    // Mock products with multiple images
    return [
      {
        id: '1',
        title: 'Omaalakasha',
        price: '50',
        location: 'Windhoek',
        region: 'Khomas',
        timeAgo: '3 days ago',
        description: 'Omaalakasha ga zima nawa, okapandi oko kena o20, ukapandi uukwawo owuna 50 wu na omafuma ga 12.',
        images: [
          { id: '1', uri: null },
          { id: '2', uri: null },
          { id: '3', uri: null }
        ],
        seller: {
          id: 'user1',
          storeName: 'Amiga',
          profileImage: null,
          phoneNumber: '+264811234567'
        }
      },
      {
        id: '2',
        title: 'Omaadi ombwa yomomeva',
        price: '50',
        location: 'Swakopmund',
        region: 'Erongo',
        timeAgo: '5 hours ago',
        description: 'Omaadi ombwa yomomeva ya tungwa ko ulongela po uku uupuka. Ina kwata kepamba lyomudilo. Omaadi ombwa oya manena mombike.',
        images: [
          { id: '1', uri: null },
          { id: '2', uri: null }
        ],
        seller: {
          id: 'user2',
          storeName: 'Namibian Art Shop',
          profileImage: null,
          phoneNumber: '+264812345678'
        }
      },
      {
        id: '3',
        title: 'Iiyuma',
        price: '50',
        location: 'Oshakati',
        region: 'Oshana',
        timeAgo: 'Yesterday',
        description: 'Iiyuma yi ikwatelela momakaya. Ya tungwa nounyuni woitjuhwena. Ina kuchilwa nayina oitakifo imwe yakalamwenyo.',
        images: [
          { id: '1', uri: null }
        ],
        seller: {
          id: 'user3',
          storeName: 'HomeDecorNamibia',
          profileImage: null,
          phoneNumber: '+264813456789'
        }
      },
      {
        id: '4',
        title: 'Omongwa',
        price: '50',
        location: 'Rundu',
        region: 'Kavango East',
        timeAgo: '2 days',
        description: 'Omongwa owa kwatekwa moshitoo shinya. Tau vaveta ko noukadhi womewo nomongwa. Omongwa tau yambididwa nawa.',
        images: [
          { id: '1', uri: null },
          { id: '2', uri: null }
        ],
        seller: {
          id: 'user4',
          storeName: 'ShortName',
          profileImage: null,
          phoneNumber: '+264814567890'
        }
      },
      {
        id: '5',
        title: 'Exactly Twenty Three Chr',
        price: '50',
        location: 'Katima Mulilo',
        region: 'Zambezi',
        timeAgo: '1 week ago',
        description: 'Eshi oyena oukadhi 23 momusholondondo. Nena otuna okuteluna etya lyonyala ndyoka ta li gandja omauyelele gokutya otu na shike apa molweendo twetu.',
        images: [
          { id: '1', uri: null },
          { id: '2', uri: null },
          { id: '3', uri: null }
        ],
        seller: {
          id: 'user5',
          storeName: 'Exactly Fifteen',
          profileImage: null,
          phoneNumber: '+264815678901'
        }
      },
      {
        id: '6',
        title: 'This Is A Very Long Product Name That Needs Truncation',
        price: '50',
        location: 'Opuwo',
        region: 'Kunene',
        timeAgo: 'Just now',
        description: 'This product has a very long name that requires truncation when displayed in the feed view. However, in this detailed view, we can show the full product name and description. The product is carefully crafted by local artisans using traditional techniques.',
        images: [
          { id: '1', uri: null }
        ],
        seller: {
          id: 'user6',
          storeName: 'This Is A Very Long Store Name That Needs Truncation',
          profileImage: null,
          phoneNumber: '+264816789012'
        }
      }
    ];
  }
};

const ProductCard = ({ title, price, location, region, timeAgo, onSaveStateChange, initialSavedState = false, product, navigation }) => {
  const [isSaved, setIsSaved] = useState(initialSavedState);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [swiping, setSwiping] = useState(false);
  const startTouchX = useRef(0);
  
  // Use product data if available, otherwise use props
  const productData = product || {
    title,
    price,
    location,
    region,
    timeAgo,
    images: [1, 2, 3], // Default mock images if no product provided
    seller: {
      storeName: "OmatalaSuperStore",
      profileImage: null,
      phoneNumber: '+264812345678'
    }
  };
  
  // Generate a consistent placeholder background color based on product ID
  const getPlaceholderColor = () => {
    // If we have a product ID, use it to generate a consistent color
    if (productData.id) {
      const hash = productData.id.split('').reduce((a, b) => {
        a = ((a << 5) - a) + b.charCodeAt(0);
        return a & a;
      }, 0);
      
      // Generate pastel-like colors that are light enough for text
      const r = ((hash & 0xFF0000) >> 16) % 200 + 55;
      const g = ((hash & 0x00FF00) >> 8) % 200 + 55;
      const b = (hash & 0x0000FF) % 200 + 55;
      
      return `rgba(${r}, ${g}, ${b}, 0.3)`;
    }
    
    // Default fallback color
    return '#F5F5F5';
  };
  
  // Get seller data from product
  const userData = productData.seller || {
    storeName: "OmatalaSuperStore",
    profileImage: null,
  };

  // Format store name: show as is if 15 chars or less, otherwise show first 15 + "..."
  const formattedStoreName = userData.storeName.length > 15 
    ? `${userData.storeName.substring(0, 15)}...` 
    : userData.storeName;

  // Format product title: show as is if 23 chars or less, otherwise show first 23 + "..."
  const formattedTitle = productData.title.length > 23
    ? `${productData.title.substring(0, 23)}...`
    : productData.title;

  // Format price to have commas for thousands
  const formatPrice = (price) => {
    // Handle if price is not a number
    if (isNaN(parseFloat(price))) return price;
    
    // Convert to number and format with commas
    return parseFloat(price).toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  };

  // Format timeAgo to be consistent
  const formatTimeAgo = (timeAgo) => {
    if (!timeAgo) return 'Recently';
    
    // If it already has "ago" in it, return as is
    if (timeAgo.includes('ago')) return timeAgo;
    
    // If it's "Yesterday", return as is
    if (timeAgo === 'Yesterday') return timeAgo;
    
    // Otherwise, add "ago" if it's missing
    return `${timeAgo} ago`;
  };

  const handleContact = async () => {
    try {
      // Open phone dialer with the number already inserted
      const phoneNumber = userData.phoneNumber || '+264812345678';
      await Linking.openURL(`tel:${phoneNumber}`);
    } catch (error) {
      console.error('Error handling contact:', error);
      // Fallback to copying to clipboard if direct dialing fails
      await Clipboard.setString(userData.phoneNumber || '+264812345678');
    }
  };

  // Get images from product data
  const images = productData.images || [1, 2, 3]; // Default mock images if none provided

  const handleSave = () => {
    const newSavedState = !isSaved;
    setIsSaved(newSavedState);
    onSaveStateChange(newSavedState);
  };

  // Handle touch start for swipe
  const handleTouchStart = (event) => {
    startTouchX.current = event.nativeEvent.locationX;
    setSwiping(true);
  };

  // Handle touch end for swipe
  const handleTouchEnd = (event) => {
    if (!swiping) return;
    
    const endTouchX = event.nativeEvent.locationX;
    const deltaX = endTouchX - startTouchX.current;
    
    // If swipe distance is significant and there's more than one image
    if (Math.abs(deltaX) > 30 && images.length > 1) {
      if (deltaX > 0) {
        // Swipe right - show previous image
        setCurrentImageIndex((prevIndex) => 
          prevIndex === 0 ? images.length - 1 : prevIndex - 1
        );
      } else {
        // Swipe left - show next image
        setCurrentImageIndex((prevIndex) => 
          prevIndex === images.length - 1 ? 0 : prevIndex + 1
        );
      }
      
      // Prevent other touch handlers from triggering
      event.stopPropagation();
    }
    
    setSwiping(false);
  };

  // Handle touch move to cancel navigation if swiping
  const handleTouchMove = (event) => {
    if (swiping) {
      const currentX = event.nativeEvent.locationX;
      const deltaX = Math.abs(currentX - startTouchX.current);
      
      // If significant horizontal movement, mark as swiping to prevent navigation
      if (deltaX > 10 && images.length > 1) {
        event.stopPropagation();
      }
    }
  };

  // Get image handler props based on whether there are multiple images
  const getImageHandlerProps = () => {
    // Only enable swiping if there are multiple images
    if (images.length > 1) {
      return {
        onTouchStart: handleTouchStart,
        onTouchEnd: handleTouchEnd,
        onTouchMove: handleTouchMove,
        ...Platform.OS === 'web' ? {} : {
          accessible: true,
          accessibilityRole: 'imagebutton',
          accessibilityLabel: 'Swipe to view more product images',
          accessibilityHint: 'Swipe left or right to navigate through product images'
        }
      };
    }
    
    // Return empty props for single image products
    return {};
  };

  // Notify parent component when mounting if already saved initially
  useEffect(() => {
    if (initialSavedState) {
      onSaveStateChange(true);
    }
  }, []);

  // Handle tap on image container to navigate to item detail
  const handleProductInfoPress = () => {
    if (navigation && product) {
      // Pass the product data along with the saved state
      const productWithSavedState = {
        ...product,
        saved: isSaved
      };
      navigation.navigate('ItemDetail', { product: productWithSavedState });
    }
  };

  return (
    <View style={styles.productCard}>
      {/* Title and Like Button Container */}
      <View style={styles.titleContainer}>
        <Text style={styles.productTitle}>{formattedTitle}</Text>
        <TouchableOpacity 
          style={styles.heartIconButton}
          onPress={handleSave}
        >
          <Ionicons 
            name={isSaved ? "heart" : "heart-outline"} 
            size={24} 
            color={isSaved ? "#FF6B00" : "#FF6B00"} 
          />
        </TouchableOpacity>
      </View>

      {/* Image Container - No TouchableOpacity here */}
      <View style={styles.imageContainer}>
        <View 
          {...getImageHandlerProps()}
          style={[
            styles.imagePlaceholder,
            { backgroundColor: getPlaceholderColor() }
          ]}
        >
          <Ionicons 
            name={images.length > 1 ? "images-outline" : "image-outline"} 
            size={40} 
            color="#666" 
          />
          <Text style={styles.placeholderText}>
            {images.length > 1 ? "Swipe to view images" : "Product image"}
          </Text>
        </View>
        
        {/* User Profile and Image Count Container */}
        <View style={styles.imageHeaderContainer}>
          {/* User Profile Section */}
          <ProfileButton 
            userData={userData}
            navigation={navigation}
          />
        </View>
        
        {/* Image Indicator Dots */}
        <View style={styles.indicatorDotsContainer}>
          {images.map((_, index) => (
            <View 
              key={index} 
              style={[
                styles.indicatorDot,
                currentImageIndex === index && styles.indicatorDotActive
              ]} 
            />
          ))}
        </View>
      </View>

      {/* Product Info - Only this area is clickable for navigation */}
      <TouchableOpacity
        activeOpacity={0.7}
        onPress={handleProductInfoPress}
      >
        <View style={styles.productInfo}>
          <Text style={styles.productPrice}>N$ {formatPrice(productData.price)}</Text>
          <View style={styles.locationContainer}>
            <Ionicons name="location-outline" size={16} color="#666" />
            <Text style={styles.locationText}>{productData.location},</Text>
            <Text style={styles.regionText}>{productData.region} Region</Text>
          </View>
          <View style={styles.timeContainer}>
            <Ionicons name="time-outline" size={16} color="#666" />
            <Text style={styles.timeText}>{formatTimeAgo(productData.timeAgo)}</Text>
          </View>
        </View>
      </TouchableOpacity>
      
      {/* Contact Button */}
      <TouchableOpacity style={styles.contactButton} onPress={handleContact}>
        <Text style={styles.contactButtonText}>CONTACT</Text>
      </TouchableOpacity>
    </View>
  );
};

const ConnectivityBanner = ({ isOnline }) => {
  const [visible, setVisible] = useState(true);
  const [timer, setTimer] = useState(null);
  
  useEffect(() => {
    // Clear any existing timers
    if (timer) clearTimeout(timer);
    
    // Reset visibility
    setVisible(true);
    
    // If online, set a timer to hide the banner after 2 seconds
    if (isOnline) {
      const newTimer = setTimeout(() => {
        setVisible(false);
      }, 2000);
      setTimer(newTimer);
    }
    
    // Cleanup
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isOnline]);
  
  if (!visible) return null;
  
  return (
    <View style={[
      styles.connectivityBanner,
      isOnline ? styles.onlineBanner : styles.offlineBanner
    ]}>
      <Text style={styles.connectivityText}>
        {isOnline ? 'Online' : 'Offline'}
      </Text>
    </View>
  );
};

const AnimatedDots = () => {
  const [dots, setDots] = useState('');
  
  useEffect(() => {
    let count = 0;
    const interval = setInterval(() => {
      count = (count + 1) % 4;
      setDots('.'.repeat(count));
    }, 300);
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <Text style={styles.dotsText}>{dots}</Text>
  );
};

const FeedScreen = ({ navigation, route }) => {
  const [activeCategory, setActiveCategory] = useState('All');
  const [hasAnySavedItems, setHasAnySavedItems] = useState(false);
  const [hasNotifications, setHasNotifications] = useState(true);
  const [menuVisible, setMenuVisible] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [showConnectivity, setShowConnectivity] = useState(false);
  const [isRefreshAttemptFailed, setIsRefreshAttemptFailed] = useState(false);
  const [cachedData, setCachedData] = useState(null);
  const [categories, setCategories] = useState(['All', 'Food', 'Craft', 'Agriculture']); // Default categories
  const windowHeight = Dimensions.get('window').height;
  const scrollViewRef = useRef(null);

  // Handle hardware back button to exit app instead of navigating back
  useExitAppOnBackButton();

  // Check for updates from the ItemDetailScreen
  useEffect(() => {
    if (route.params?.updatedProduct) {
      const { id, saved } = route.params.updatedProduct;
      // Update the saved items state
      handleSaveStateChange(id, saved);
      
      // Clear the params to prevent repeated processing
      navigation.setParams({ updatedProduct: null });
    }
  }, [route.params?.updatedProduct]);

  // Handle updates from the ItemDetailScreen when it modifies a product's saved state
  // This handles the case where the user toggled the heart icon in ItemDetailScreen
  useEffect(() => {
    if (route.params?.product?.id) {
      const product = route.params.product;
      handleSaveStateChange(product.id, product.saved);
      
      // Clear the params to prevent repeated processing
      navigation.setParams({ product: null });
    }
  }, [route.params?.product]);

  // Load categories from AsyncStorage/backend
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesWithAll = await getCategoriesForFeed();
        setCategories(categoriesWithAll);
        console.log('Categories loaded for FeedScreen:', categoriesWithAll);
      } catch (error) {
        console.error('Error loading categories in FeedScreen:', error);
        // Keep default categories on error
      }
    };

    loadCategories();
  }, []);

  // Refresh categories when screen is focused
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', async () => {
      try {
        console.log('FeedScreen focused - refreshing categories...');
        const categoriesWithAll = await getCategoriesForFeed(true); // Force refresh
        setCategories(categoriesWithAll);
        console.log('Categories refreshed on focus:', categoriesWithAll);
      } catch (error) {
        console.error('Error refreshing categories on focus:', error);
      }
    });

    return unsubscribe;
  }, [navigation]);

  // Get products from mock API
  const mockProducts = mockAPI.getProductData();
  
  // Convert to object format for state
  const initialProductsObj = mockProducts.reduce((acc, product) => {
    acc[product.id] = product;
    return acc;
  }, {});

  // Track the saved state of each product
  const [savedItems, setSavedItems] = useState(
    Object.keys(initialProductsObj).reduce((acc, id) => {
      acc[id] = false;
      return acc;
    }, {})
  );

  // Maintain cached products data
  const [products, setProducts] = useState(initialProductsObj);

  // Initialize the cached data when the component first loads
  useEffect(() => {
    setCachedData(initialProductsObj);
  }, []);

  const handleSaveStateChange = (productId, isSaved) => {
    // Update the saved items state
    setSavedItems(prev => ({
      ...prev,
      [productId]: isSaved
    }));
  };

  // Update hasAnySavedItems whenever savedItems changes
  useEffect(() => {
    const anySaved = Object.values(savedItems).some(isSaved => isSaved);
    setHasAnySavedItems(anySaved);
  }, [savedItems]);

  const handleCategoryPress = (category) => {
    // Check if we're online before allowing category changes
    if (!isOnline) {
      handleNetworkChange(false); // Show offline message
      return;
    }
    
    setActiveCategory(category);
    
    // Scroll to top when changing categories
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({ x: 0, y: 0, animated: true });
    }
    
    // In a real app, this would fetch new data based on the selected category
    // For now, we'll use the same data for all categories
  };

  const toggleMenu = () => {
    setMenuVisible(!menuVisible);
  };

  const handleLogout = async () => {
    setMenuVisible(false);

    try {
      // Step 1: Get user's phone number for recycling verification code
      const userData = await getUserData();
      const phoneNumber = userData?.phoneNumber;

      // Step 2: Call recycle verification code endpoint if we have phone number
      if (phoneNumber) {
        console.log('🔄 Recycling verification code for:', phoneNumber);
        try {
          const recycleResult = await awsApi.recycleVerificationCode(phoneNumber);
          console.log('✅ Verification code recycle result:', recycleResult.message);
        } catch (recycleError) {
          console.warn('⚠️ Verification code recycling failed (continuing with logout):', recycleError);
          // Continue with logout even if recycling fails
        }
      } else {
        console.log('ℹ️ No phone number found, skipping verification code recycling');
      }

      // Step 3: Proceed with normal logout process
      await clearSessionToken();
      await clearUserId();
      console.log('✅ User logged out successfully');
    } catch (error) {
      console.error('❌ Error during logout:', error);
      // Always proceed to login screen even if logout process fails
    }

    navigation.navigate('PhoneNumber');
  };

  const navigateToSearch = () => {
    if (!isOnline) {
      handleNetworkChange(false); // Show offline message
      return;
    }
    navigation.navigate('Search');
  };

  // Navigate to notifications screen
  const navigateToNotifications = () => {
    if (!isOnline) {
      handleNetworkChange(false); // Show offline message
      return;
    }
    navigation.navigate('Notifications');
  };

  // In the FeedScreen component
  // Add a function to navigate to saved items
  const navigateToSavedItems = () => {
    if (!isOnline) {
      handleNetworkChange(false); // Show offline message
      return;
    }
    navigation.navigate('SavedItems');
  };

  // Simulate a network request
  const simulateNetworkRequest = () => {
    return new Promise((resolve, reject) => {
      // Simulate network delay
      setTimeout(() => {
        if (isOnline) {
          // This is where you would fetch fresh data from your API
          // For now, we'll use our mock API
          const freshData = mockAPI.getProductData().reduce((acc, product) => {
            acc[product.id] = product;
            return acc;
          }, {});
          resolve(freshData);
        } else {
          reject(new Error('Network request failed'));
        }
      }, 2000);
    });
  };

  const handleNetworkChange = (online) => {
    setIsOnline(online);
    setShowConnectivity(true);
    
    // Hide the online banner after 2 seconds
    if (online) {
      setTimeout(() => {
        setShowConnectivity(false);
        // Reset the refresh attempt failed flag when online
        setIsRefreshAttemptFailed(false);
      }, 2000);
    }
  };

  // Render products based on the cached data when offline
  const renderProducts = () => {
    const productsToRender = isRefreshAttemptFailed ? cachedData : products;
    
    if (!productsToRender) return null;
    
    return Object.keys(productsToRender).map(productId => (
      <ProductCard
        key={productId}
        initialSavedState={savedItems[productId]}
        onSaveStateChange={(isSaved) => handleSaveStateChange(productId, isSaved)}
        product={productsToRender[productId]}
        navigation={navigation}
      />
    ));
  };

  const handleLogoPress = () => {
    // Scroll to top
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({ x: 0, y: 0, animated: true });
    }
    
    // Reset to "All" category
    setActiveCategory('All');
    
    // Check if we're online before attempting to refresh
    if (!isOnline) {
      setIsRefreshAttemptFailed(true);
      handleNetworkChange(false); // Show offline message
      return;
    }
    
    // Start refreshing
    setRefreshing(true);
    setIsRefreshAttemptFailed(false);

    // Refresh categories first
    const refreshCategories = async () => {
      try {
        console.log('Refreshing categories during logo press...');
        const categoriesWithAll = await getCategoriesForFeed(true); // Force refresh
        setCategories(categoriesWithAll);
        console.log('Categories refreshed during logo press:', categoriesWithAll);
      } catch (error) {
        console.error('Error refreshing categories during logo press:', error);
      }
    };

    // Refresh categories and products
    Promise.all([
      refreshCategories(),
      simulateNetworkRequest()
    ])
      .then(([_, freshData]) => {
        // Success - update the UI with fresh data
        setRefreshing(false);
        setProducts(freshData);
        setCachedData(freshData); // Update the cache with fresh data
      })
      .catch(error => {
        // Failure - show offline message
        console.error('Refresh failed:', error);
        setRefreshing(false);
        setIsRefreshAttemptFailed(true);
        handleNetworkChange(false);
      });
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style="auto" />
      {/* Main App Frame */}
      <View style={styles.mainFrame}>
        {/* Feed Content - Scrollable */}
        <View style={styles.feedContent}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={handleLogoPress}>
              <Text style={styles.logo}>Omatala</Text>
            </TouchableOpacity>
            <View style={styles.headerRight}>
              <TouchableOpacity onPress={navigateToNotifications}>
                <View style={styles.iconContainer}>
                  <Ionicons 
                    name={hasNotifications ? "notifications" : "notifications-outline"} 
                    size={24} 
                    color="#FF6B00" 
                  />
                  {hasNotifications && <View style={styles.notificationDot} />}
                </View>
              </TouchableOpacity>
              <TouchableOpacity onPress={navigateToSavedItems}>
                <View style={styles.savedItemsContainer}>
                  <Ionicons 
                    name={hasAnySavedItems ? "heart" : "heart-outline"} 
                    size={24} 
                    color="#FF6B00" 
                  />
                  {hasAnySavedItems && <View style={styles.savedDot} />}
                </View>
              </TouchableOpacity>
              <TouchableOpacity onPress={toggleMenu} style={styles.savedItemsContainer}>
                <Ionicons name="ellipsis-vertical" size={24} color="#FF6B00" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Search Bar */}
          <TouchableOpacity 
            style={styles.searchContainer}
            onPress={navigateToSearch}
            activeOpacity={0.8}
          >
            <View style={styles.dummySearchInput}>
              <Text style={styles.searchPlaceholder}>What are you looking for?</Text>
            </View>
            <View style={styles.searchButton}>
              <Ionicons name="search" size={20} color="#FFF" />
            </View>
          </TouchableOpacity>

          {/* Categories */}
          <CategoriesScrollView
            categories={categories}
            activeCategory={activeCategory}
            onCategoryPress={handleCategoryPress}
          />

          {/* Product List */}
          <ScrollView 
            ref={scrollViewRef}
            style={styles.productList}
            keyboardShouldPersistTaps="handled"
            keyboardDismissMode="on-drag"
          >
            {refreshing ? (
              <View style={styles.refreshingContainer}>
                <Ionicons name="refresh" size={24} color="#FF6B00" style={styles.spinningIcon} />
                <View style={styles.refreshingTextContainer}>
                  <Text style={styles.refreshingText}>Refreshing</Text>
                  <AnimatedDots />
                </View>
              </View>
            ) : (
              <>
                {isRefreshAttemptFailed && (
                  <View style={styles.cacheNotification}>
                    <Ionicons name="information-circle-outline" size={18} color="#666" />
                    <Text style={styles.cacheText}>Showing cached data</Text>
                  </View>
                )}
                {renderProducts()}
              </>
            )}
            <View style={styles.bottomPadding} />
          </ScrollView>
        </View>

        {/* Bottom Navigation */}
        <BottomNavigation currentTab="home" navigation={navigation} />

        {/* Connectivity Banner */}
        {showConnectivity && (
          <ConnectivityBanner isOnline={isOnline} />
        )}

        {/* Logout Menu Modal */}
        <Modal
          transparent={true}
          visible={menuVisible}
          animationType="fade"
          onRequestClose={() => setMenuVisible(false)}
        >
          <TouchableWithoutFeedback onPress={() => setMenuVisible(false)}>
            <View style={styles.modalOverlay}>
              <TouchableWithoutFeedback>
                <View style={styles.menuContainer}>
                  <View style={styles.menuHeader}>
                    <Text style={styles.menuTitle}>Options</Text>
                    <TouchableOpacity onPress={() => setMenuVisible(false)}>
                      <Ionicons name="close" size={24} color="#666" />
                    </TouchableOpacity>
                  </View>
                  <TouchableOpacity 
                    style={styles.logoutButton}
                    onPress={handleLogout}
                  >
                    <Text style={styles.logoutButtonText}>LOGOUT</Text>
                  </TouchableOpacity>
                </View>
              </TouchableWithoutFeedback>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  // App layout styles
  safeArea: {
    flex: 1,
    backgroundColor: '#FFF',
  },
  mainFrame: {
    flex: 1,
    position: 'relative',
  },
  feedContent: {
    flex: 1,
    paddingBottom: 60, // Height of the navigation bar
  },
  navigationBar: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: 60,
    backgroundColor: '#FFF',
    borderTopWidth: 1,
    borderTopColor: '#EEE',
    zIndex: 1000,
  },
  
  // Header styles
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 16,
  },
  logo: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF6B00',
    fontFamily: 'Fredoka-Regular',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  savedItemsContainer: {
    position: 'relative',
    padding: 5,
  },
  savedDot: {
    position: 'absolute',
    right: 2,
    top: 2,
    backgroundColor: 'red',
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  iconContainer: {
    position: 'relative',
    padding: 5,
    marginRight: 10,
  },
  notificationDot: {
    position: 'absolute',
    right: 2,
    top: 2,
    backgroundColor: 'red',
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  
  // Search styles
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  dummySearchInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: '#FF6B00',
    borderRadius: 20,
    paddingHorizontal: 16,
    marginRight: 8,
    justifyContent: 'center',
  },
  searchPlaceholder: {
    color: '#999',
  },
  searchButton: {
    width: 40,
    height: 40,
    backgroundColor: '#FF6B00',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  

  
  // Product List styles
  productList: {
    flex: 1,
  },
  bottomPadding: {
    height: 80,
  },
  
  // Product Card styles
  productCard: {
    backgroundColor: '#FFF',
    marginHorizontal: 16,
    marginVertical: 10,
    borderRadius: 16,
    // Android shadow
    elevation: 8,
    // iOS shadow
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    padding: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  productTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 8,
    lineHeight: 22,
  },
  heartIconButton: {
    padding: 4,
  },
  
  // Image Container styles
  imageContainer: {
    height: 200,
    backgroundColor: '#F5F5F5',
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
    marginBottom: 12,
  },
  imagePlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
  },
  placeholderText: {
    color: '#666',
    fontSize: 14,
    marginTop: 8,
  },
  imageHeaderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
    paddingTop: 8,
  },
  indicatorDotsContainer: {
    position: 'absolute',
    bottom: 12,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicatorDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255,255,255,0.6)',
    marginHorizontal: 3,
  },
  indicatorDotActive: {
    backgroundColor: '#FF6B00',
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  
  // Product Info styles
  productInfo: {
    paddingHorizontal: 4,
  },
  productPrice: {
    fontSize: 18,
    color: '#FF6B00',
    fontWeight: 'bold',
    marginBottom: 10,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  locationText: {
    marginLeft: 4,
    color: '#666',
    fontSize: 13,
  },
  regionText: {
    marginLeft: 4,
    color: '#666',
    fontSize: 13,
    fontStyle: 'italic',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  timeText: {
    marginLeft: 4,
    color: '#666',
    fontSize: 13,
  },
  contactButton: {
    backgroundColor: '#FF6B00',
    paddingVertical: 10,
    borderRadius: 20,
    alignItems: 'center',
    marginTop: 4,
  },
  contactButtonText: {
    color: '#FFF',
    fontWeight: 'bold',
    fontSize: 14,
  },
  
  // Menu styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  menuContainer: {
    backgroundColor: '#FFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 20,
    paddingBottom: 30,
    paddingTop: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  menuHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  menuTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  logoutButton: {
    backgroundColor: '#FF6B00',
    paddingVertical: 12,
    borderRadius: 20,
    alignItems: 'center',
    marginBottom: 10,
  },
  logoutButtonText: {
    color: '#FFF',
    fontWeight: 'bold',
  },
  
  // Refreshing and connectivity styles
  refreshingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  spinningIcon: {
    transform: [{ rotate: '45deg' }],
    marginBottom: 10,
  },
  refreshingTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  refreshingText: {
    color: '#666',
    fontSize: 14,
  },
  dotsText: {
    color: '#666',
    fontSize: 14,
    minWidth: 24, // Space for 3 dots
    marginLeft: 2,
  },
  connectivityBanner: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 60, // Just above the navigation bar
    padding: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  offlineBanner: {
    backgroundColor: '#999',
  },
  onlineBanner: {
    backgroundColor: '#4CAF50',
  },
  connectivityText: {
    color: '#FFF',
    fontWeight: 'bold',
  },
  cacheNotification: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F5F5',
    padding: 8,
    borderRadius: 4,
    marginHorizontal: 16,
    marginBottom: 16,
  },
  cacheText: {
    color: '#666',
    fontSize: 14,
    marginLeft: 8,
  },
});

export default FeedScreen; 