# Backend Integration Guide - <PERSON><PERSON><PERSON><PERSON><PERSON>OOF DEPLOYMENT STEPS

This document outlines the **BULLETPROOF** process for deploying new backend endpoints with AWS Lambda and API Gateway using Terraform. These steps have been battle-tested and will prevent ALL common deployment failures.

## 🚨 CRITICAL FAILURE POINTS - READ FIRST

**These are the TOP 5 issues that WIL<PERSON> break your deployment if not handled:**

### 1. IAM Policy Limit (10 Policies Maximum)
**Problem**: AWS limits roles to 10 attached policies. Creating new policies will fail.
**Solution**: ALWAYS combine new permissions with existing policies.
```bash
# Check current policy count BEFORE starting
aws iam list-attached-role-policies --role-name Omatala-online-auth-role-dev
# If 9+ policies exist, add permissions to existing policy instead of creating new one
```

### 2. UUID Module Missing in Lambda
**Problem**: `require('uuid')` fails with `Runtime.ImportModuleError`
**Solution**: Use built-in crypto module instead:
```javascript
// ❌ WRONG - Will cause Runtime.ImportModuleError
const { v4: uuidv4 } = require('uuid');

// ✅ CORRECT - Use built-in crypto module
const { randomUUID } = require('crypto');
const id = randomUUID(); // Instead of uuidv4()
```

### 3. API Gateway "No Integration Defined" Error
**Problem**: Terraform tries to deploy API Gateway before integrations exist
**Solution**: Use incremental deployment pattern (detailed below)

### 4. Variable Name Mismatches
**Problem**: Using wrong variable names causes reference errors
**Solution**: Always use these exact variable names:
```hcl
# ✅ CORRECT variable names
var.region          # NOT var.aws_region
var.account_id      # NOT var.aws_account_id
var.environment     # NOT var.env
```

### 5. Missing Dependencies During Incremental Deployment
**Problem**: Commented resources still referenced in outputs/dependencies
**Solution**: Comment out ALL references including outputs and stage resources

## ⚠️ MANDATORY PRE-DEPLOYMENT CHECKLIST

**Complete this checklist BEFORE starting any deployment:**

- [ ] Check IAM policy count: `aws iam list-attached-role-policies --role-name Omatala-online-auth-role-dev`
- [ ] Verify Lambda code uses only built-in modules (`crypto`, `@aws-sdk/*`)
- [ ] Confirm variable names match exactly: `var.region`, `var.account_id`, `var.environment`
- [ ] Have this guide open for reference during deployment
- [ ] Backup current working infrastructure: `terraform plan > backup-plan.txt`

## ⚠️ CRITICAL: Follow These Steps in Exact Order

### Step 1: Create Lambda Function Code First
Before touching Terraform, create the actual Lambda function code:

```bash
# Navigate to infrastructure directory
cd infrastructure

# Create a temporary Lambda file
cat > temp_lambda.js << 'EOF'
const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
const { DynamoDBDocumentClient, UpdateCommand } = require("@aws-sdk/lib-dynamodb");
const { randomUUID } = require('crypto'); // Use built-in crypto for UUIDs

const dbClient = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(dbClient);

exports.handler = async (event) => {
  console.log("Lambda triggered with event:", JSON.stringify(event, null, 2));
  
  const headers = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers": "Content-Type",
    "Access-Control-Allow-Methods": "POST, OPTIONS"
  };

  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: headers,
      body: JSON.stringify({ message: 'CORS preflight successful' })
    };
  }

  try {
    // Your Lambda logic here
    
    return {
      statusCode: 200,
      headers: headers,
      body: JSON.stringify({
        success: true,
        message: "Function executed successfully"
      })
    };
  } catch (error) {
    console.error("Error:", error);
    return {
      statusCode: 500,
      headers: headers,
      body: JSON.stringify({
        error: "Internal server error",
        details: error.message
      })
    };
  }
};
EOF

# Rename to index.js and create zip
mv temp_lambda.js index.js
powershell -Command "Compress-Archive -Path index.js -DestinationPath your_function.zip -Force"
```

### Step 2: Create Lambda Function via AWS CLI
**NEVER** let Terraform create the Lambda function first. Always create manually:

```bash
# Create the Lambda function
aws lambda create-function \
  --function-name Omatala-online-your-function-dev \
  --runtime nodejs18.x \
  --role arn:aws:iam::636428917853:role/Omatala-online-auth-role-dev \
  --handler index.handler \
  --zip-file fileb://your_function.zip \
  --environment 'Variables={USERS_TABLE_NAME=Omatala-online-users-dev,ENVIRONMENT=dev}'

# Add API Gateway permission
aws lambda add-permission \
  --function-name Omatala-online-your-function-dev \
  --statement-id AllowAPIGatewayInvokeYourFunction \
  --action lambda:InvokeFunction \
  --principal apigateway.amazonaws.com \
  --source-arn "arn:aws:execute-api:us-east-1:636428917853:0q229lu1qk/dev/POST/your-endpoint"
```

### Step 3: Create API Gateway Integration via AWS CLI
Create the integration manually to avoid Terraform deployment conflicts:

```bash
# Get the resource ID for your endpoint (replace 'your-endpoint' with actual path)
RESOURCE_ID=$(aws apigateway get-resources --rest-api-id 0q229lu1qk --query "items[?pathPart=='your-endpoint'].id" --output text)

# Create the integration
aws apigateway put-integration \
  --rest-api-id 0q229lu1qk \
  --resource-id $RESOURCE_ID \
  --http-method POST \
  --type AWS_PROXY \
  --integration-http-method POST \
  --uri "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:636428917853:function:Omatala-online-your-function-dev/invocations"

# Add method response
aws apigateway put-method-response \
  --rest-api-id 0q229lu1qk \
  --resource-id $RESOURCE_ID \
  --http-method POST \
  --status-code 200 \
  --response-parameters "method.response.header.Access-Control-Allow-Origin=true"

# Add integration response
cat > integration-response.json << 'EOF'
{
  "method.response.header.Access-Control-Allow-Origin": "'*'"
}
EOF

aws apigateway put-integration-response \
  --rest-api-id 0q229lu1qk \
  --resource-id $RESOURCE_ID \
  --http-method POST \
  --status-code 200 \
  --response-parameters file://integration-response.json
```

### Step 4: Import Resources into Terraform State
**CRITICAL**: Import the manually created resources into Terraform:

```bash
# Import Lambda function
terraform import module.lambda.aws_lambda_function.your_function_lambda Omatala-online-your-function-dev

# Import Lambda permission
terraform import module.lambda.aws_lambda_permission.api_gateway_your_function_permission Omatala-online-your-function-dev/AllowAPIGatewayInvokeYourFunction

# Import API Gateway integration
terraform import module.lambda.aws_api_gateway_integration.your_function_post 0q229lu1qk/$RESOURCE_ID/POST

# Import method response
terraform import module.lambda.aws_api_gateway_method_response.your_function_post_200 0q229lu1qk/$RESOURCE_ID/POST/200

# Import integration response
terraform import module.lambda.aws_api_gateway_integration_response.your_function_post_200 0q229lu1qk/$RESOURCE_ID/POST/200
```

### Step 5: Apply Terraform Configuration
Only NOW run terraform apply:

```bash
terraform apply -auto-approve
```

### Step 6: Clean Up Temporary Files
```bash
rm index.js your_function.zip integration-response.json
```

## 🚨 BATTLE-TESTED DEPLOYMENT PATTERNS

### Pattern A: The "Isolation-First" Approach (Most Reliable)
**When to Use**: Adding new Lambda functions with API Gateway integrations when Terraform keeps failing

**The Problem**: Terraform tries to create everything at once, leading to dependency conflicts and "No integration defined for method" errors.

**The Solution**:
```bash
# Step 1: Create Lambda function ONLY (without API Gateway integration)
terraform apply -target="module.lambda.aws_lambda_function.your_function_name" -auto-approve

# Step 2: Create API Gateway method and method response ONLY
terraform apply -target="module.lambda.aws_api_gateway_method_response.your_function_post_200" -auto-approve

# Step 3: Create the integration
terraform apply -target="module.lambda.aws_api_gateway_integration.your_function_post" -auto-approve

# Step 4: Create integration response
terraform apply -target="module.lambda.aws_api_gateway_integration_response.your_function_post_200" -auto-approve

# Step 5: Full deployment
terraform apply -auto-approve
```

### Pattern B: The "Deployment Bypass" Method (Nuclear Option)
**When to Use**: When API Gateway deployment keeps failing due to missing integrations

**The Problem**: Terraform deployment resource triggers even when targeting specific resources.

**The Solution**:
```bash
# Step 1: Temporarily comment out deployment and stage resources
# In modules/api_gateway/main.tf, comment out:
# - aws_api_gateway_deployment.api_deployment
# - aws_api_gateway_stage.api_stage
# Also comment out related outputs in modules/api_gateway/outputs.tf and main outputs.tf

# Step 2: Apply Lambda integrations without deployment
terraform apply -target="module.lambda.aws_api_gateway_integration.your_function" -auto-approve

# Step 3: Restore deployment and stage resources (uncomment)

# Step 4: Full deployment
terraform apply -auto-approve
```

### Pattern C: The "AWS CLI Bypass" (Emergency Only)
**When to Use**: When Terraform is completely stuck or corrupted

**The Solution**:
```bash
# Update Lambda function directly via AWS CLI
cd modules/lambda
zip your_function.zip index.js
aws lambda update-function-code \
  --function-name "Omatala-online-your-function-dev" \
  --zip-file fileb://your_function.zip

# Import the updated function back to Terraform state
terraform import module.lambda.aws_lambda_function.your_function "Omatala-online-your-function-dev"

# Continue with normal Terraform workflow
terraform apply
```

## 🚨 CRITICAL: IAM PERMISSIONS FOR DYNAMODB ACCESS

**⚠️ MOST COMMON FAILURE POINT**: Lambda functions getting 403 errors when accessing DynamoDB

### The Problem
When adding new Lambda functions that access DynamoDB tables, you MUST ensure proper IAM permissions are granted. Without these, you'll get 403 Forbidden errors on the frontend even though the API Gateway integration works.

### The Solution: Always Check IAM Policy Limits
AWS IAM roles have a **10 policy limit**. Our Lambda role is likely at this limit. You have two options:

#### Option A: Add Permissions to Existing Policy (RECOMMENDED)
```hcl
# In modules/lambda/main.tf, update an existing related policy
resource "aws_iam_policy" "verify_code_dynamodb_policy" {
  name        = "${var.project_name}-verify-code-dynamodb-${var.environment}"
  description = "Allow verify-code and related Lambdas to access DynamoDB tables"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = [
          "dynamodb:Query",
          "dynamodb:GetItem",
          "dynamodb:PutItem",
          "dynamodb:DeleteItem",
          "dynamodb:UpdateItem",
          "dynamodb:BatchGetItem"
        ]
        Resource = [
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.verification_codes_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.verification_codes_recycle_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.users_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.sessions_table_name}"
        ]
      }
    ]
  })
}
```

#### Option B: Check Current Policy Count
```bash
# Check how many policies are attached to the Lambda role
aws iam list-attached-role-policies --role-name Omatala-online-auth-role-dev --region us-east-1

# If at 10 policy limit, you MUST combine policies instead of creating new ones
```

### Required DynamoDB Actions for Most Lambda Functions
```hcl
"dynamodb:Query",        # Read multiple items
"dynamodb:GetItem",      # Read single item
"dynamodb:PutItem",      # Create new item
"dynamodb:DeleteItem",   # Delete item
"dynamodb:UpdateItem",   # Modify existing item
"dynamodb:BatchGetItem"  # Read multiple items efficiently
```

### Table ARN Format
```hcl
"arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.table_name}"
"arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.table_name}/index/${var.index_name}"  # For GSI access
```

### ⚠️ CRITICAL DEPLOYMENT ORDER FOR IAM CHANGES
1. **Update IAM policy first** (add new table permissions)
2. **Deploy IAM changes**: `terraform apply -target="module.lambda.aws_iam_policy.your_policy" -auto-approve`
3. **Then deploy Lambda function** with the new code
4. **Test immediately** - 403 errors mean missing permissions

## ⚠️ NEVER DO THESE THINGS

1. **NEVER** run `terraform apply` before creating Lambda function manually
2. **NEVER** let Terraform create API Gateway integrations first
3. **NEVER** skip the import step
4. **NEVER** use dynamic imports in React Native (`await import()`)
5. **NEVER** forget to create the zip file first
6. **NEVER** try to create everything at once - use incremental deployment
7. **NEVER** ignore "No integration defined for method" errors - stop immediately
8. **NEVER** create new IAM policies when at the 10 policy limit - combine existing ones instead
9. **NEVER** deploy Lambda functions without ensuring DynamoDB permissions exist first

## 🎯 Critical Success Indicators

**You know the deployment worked when**:
1. `terraform plan` shows "No changes"
2. API Gateway endpoints return expected responses
3. CloudWatch logs show function executions
4. No "drift detected" warnings
5. AWS Console shows all resources properly connected

## Why This Process Works

1. **Manual Creation First**: Avoids Terraform dependency conflicts
2. **Import Strategy**: Brings existing resources under Terraform management
3. **Proper Ordering**: Ensures all dependencies exist before deployment
4. **No Race Conditions**: Eliminates timing issues between resource creation
5. **Incremental Deployment**: Reduces complexity and failure points

Follow these exact steps and you'll avoid the deployment hell that wastes hours of time.

## 🔧 Common Deployment Issues & Proven Solutions

### Issue: "No integration defined for method"
**Root Cause**: API Gateway deployment triggered before integration exists
**Immediate Action**: STOP deployment immediately
**Solution**: Use Pattern C (Bulletproof Incremental Deployment)

## 🛡️ BULLETPROOF INCREMENTAL DEPLOYMENT PATTERN

**When you encounter "No integration defined for method" error, follow these EXACT steps:**

### Step 1: Comment Out Deployment Dependencies
```hcl
# In modules/api_gateway/main.tf - Comment out the deployment resource
# resource "aws_api_gateway_deployment" "api_deployment" {
#   depends_on = [
#     aws_api_gateway_integration.cors_integration,
#     # ... other integrations
#   ]
#   rest_api_id = aws_api_gateway_rest_api.api.id
#   # ... rest of deployment config
# }

# Also comment out the stage resource
# resource "aws_api_gateway_stage" "api_stage" {
#   deployment_id = aws_api_gateway_deployment.api_deployment.id
#   # ... rest of stage config
# }
```

### Step 2: Comment Out Related Outputs
```hcl
# In modules/api_gateway/outputs.tf
# output "api_stage_arn" {
#   value = aws_api_gateway_stage.api_stage.arn
# }

# In infrastructure/outputs.tf
# output "api_stage_url" {
#   value = module.api_gateway.api_stage_invoke_url
# }
```

### Step 3: Apply Lambda Functions First
```bash
terraform apply -target="module.lambda.aws_lambda_function.your_function_lambda" -auto-approve
```

### Step 4: Apply API Gateway Resources and Methods
```bash
terraform apply -target="module.api_gateway.aws_api_gateway_resource.your_resource" \
                -target="module.api_gateway.aws_api_gateway_method.your_method_post" \
                -target="module.api_gateway.aws_api_gateway_method.your_method_options" \
                -auto-approve
```

### Step 5: Apply Method Responses
```bash
terraform apply -target="module.lambda.aws_api_gateway_method_response.your_function_post_200" \
                -auto-approve
```

### Step 6: Apply Integrations
```bash
terraform apply -target="module.lambda.aws_api_gateway_integration.your_function_post" \
                -auto-approve
```

### Step 7: Apply Integration Responses
```bash
terraform apply -target="module.lambda.aws_api_gateway_integration_response.your_function_post_200" \
                -auto-approve
```

### Step 8: Apply Lambda Permissions
```bash
terraform apply -target="module.lambda.aws_lambda_permission.api_gateway_your_function_permission" \
                -auto-approve
```

### Step 9: Apply CORS Integrations
```bash
terraform apply -target="module.api_gateway.aws_api_gateway_integration.your_function_options" \
                -target="module.api_gateway.aws_api_gateway_method_response.your_function_options_200" \
                -target="module.api_gateway.aws_api_gateway_integration_response.your_function_options_200" \
                -auto-approve
```

### Step 10: Uncomment and Update Deployment
```hcl
# Uncomment deployment resource and add your new integrations to dependencies
resource "aws_api_gateway_deployment" "api_deployment" {
  depends_on = [
    aws_api_gateway_integration.cors_integration,
    # ... existing integrations
    aws_api_gateway_integration.your_function_options,  # Add your new ones
    aws_api_gateway_integration.your_function_verify_options
  ]

  triggers = {
    redeployment = sha1(jsonencode([
      # ... existing resources
      aws_api_gateway_resource.your_function.id,        # Add your new ones
      aws_api_gateway_method.your_function_post.id,
      # ... rest of triggers
    ]))
  }
}
```

### Step 11: Final Deployment
```bash
terraform apply -auto-approve
```

### Step 12: Verify Success
```bash
terraform plan  # Should show "No changes"
curl -X POST https://your-api-url/your-endpoint  # Test the endpoint
```

### Issue: Lambda function not updating
**Root Cause**: Source code hash not changing or permission conflicts
**Solution**:
```bash
# Force update via AWS CLI
aws lambda update-function-code --function-name "your-function" --zip-file fileb://function.zip
# Then import to Terraform state
terraform import module.lambda.aws_lambda_function.your_function "actual-aws-function-name"
```

### Issue: Terraform state corruption
**Root Cause**: Manual AWS changes or failed deployments
**Solution**:
```bash
# Remove from state and re-import
terraform state rm problematic.resource
terraform import problematic.resource actual-aws-id
terraform refresh
```

### Issue: "Cannot exceed quota for PoliciesPerRole: 10"
**Root Cause**: AWS limits IAM roles to 10 attached policies maximum
**Immediate Action**: DO NOT create new IAM policy
**Solution**: Add permissions to existing policy

```hcl
# ❌ WRONG - Will hit 10 policy limit
resource "aws_iam_policy" "new_function_dynamodb_policy" {
  name = "new-function-policy"
  # ... policy definition
}

# ✅ CORRECT - Add to existing policy
resource "aws_iam_policy" "check_session_dynamodb_policy" {
  name = "${var.project_name}-check-session-dynamodb-${var.environment}"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "dynamodb:Query",
          "dynamodb:GetItem",
          "dynamodb:PutItem",      # Add needed actions
          "dynamodb:UpdateItem",   # Add needed actions
          "dynamodb:BatchGetItem",
          "dynamodb:Scan"
        ]
        Resource = [
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.users_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.sessions_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.categories_table_name}",
          "arn:aws:dynamodb:${var.region}:${var.account_id}:table/${var.your_new_table_name}"  # Add your table
        ]
      }
    ]
  })
}

# Remove the separate policy and attachment
# resource "aws_iam_policy" "new_function_dynamodb_policy" { ... }
# resource "aws_iam_role_policy_attachment" "new_function_attach" { ... }
```

### Issue: 403 Forbidden errors from Lambda functions
**Root Cause**: Missing DynamoDB permissions for Lambda function
**Symptoms**: API Gateway returns 200, but Lambda gets 403 when accessing DynamoDB
**Immediate Action**: Check IAM policies and DynamoDB permissions
**Solution**:
```bash
# 1. Check current policy count (max 10 per role)
aws iam list-attached-role-policies --role-name Omatala-online-auth-role-dev

# 2. If at limit, update existing policy instead of creating new one
# 3. Add required DynamoDB table ARNs to existing policy
# 4. Deploy IAM changes first: terraform apply -target="module.lambda.aws_iam_policy.existing_policy"
# 5. Then deploy Lambda function
```

### Issue: Dependency cycles
**Root Cause**: Circular dependencies in Terraform configuration
**Solution**: Use `depends_on` explicitly and create resources in stages using targeted applies

### Issue: "Resource already exists" errors
**Root Cause**: Resource exists in AWS but not in Terraform state
**Solution**:
```bash
# Import existing resource instead of creating new one
terraform import module.lambda.aws_lambda_function.existing_function "actual-function-name"
```

## 🚀 Emergency Recovery Commands

```bash
# Check what Terraform thinks vs AWS reality
terraform plan -detailed-exitcode

# Force Terraform to see current AWS state
terraform refresh

# Remove corrupted resource from state (doesn't delete from AWS)
terraform state rm module.lambda.aws_lambda_function.problematic_function

# Import existing AWS resource to Terraform state
terraform import module.lambda.aws_lambda_function.your_function "actual-aws-function-name"

# Target specific problematic resource
terraform apply -target="specific.resource.name" -auto-approve

# Emergency Lambda update (bypasses Terraform completely)
cd modules/lambda && zip function.zip index.js
aws lambda update-function-code --function-name "Omatala-online-function-dev" --zip-file fileb://function.zip
```

## 📋 The "Comment-Uncomment" Pattern (Proven Effective)

**Why it works**: Temporarily removes problematic dependencies to allow incremental creation

**Files commonly affected**:
- `modules/api_gateway/main.tf` (deployment and stage resources)
- `modules/api_gateway/outputs.tf` (stage-dependent outputs)
- `outputs.tf` (main outputs referencing stage outputs)

**How to use**:
1. **Comment out** deployment resources that are causing conflicts
2. **Apply** the core resources (Lambda, integrations)
3. **Uncomment** deployment resources
4. **Apply** final deployment

## Adding New Endpoints - Complete Template

### 1. Add to modules/api_gateway/main.tf
```hcl
# API Gateway Resource
resource "aws_api_gateway_resource" "your_endpoint" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "your-endpoint"
}

# POST Method
resource "aws_api_gateway_method" "your_endpoint_post" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.your_endpoint.id
  http_method   = "POST"
  authorization = "NONE"
}

# OPTIONS Method for CORS
resource "aws_api_gateway_method" "your_endpoint_options" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.your_endpoint.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

# CORS Integration and Responses (copy from existing endpoints)
```

### 2. Add to modules/lambda/main.tf
```hcl
# Archive file for Lambda function
data "archive_file" "your_function_lambda" {
  type        = "zip"
  output_path = "modules/lambda/your_function.zip"

  source {
    content = <<EOT
// Your Lambda function code here
EOT
    filename = "index.js"
  }
}

# Lambda function (will be imported, not created)
resource "aws_lambda_function" "your_function_lambda" {
  filename         = data.archive_file.your_function_lambda.output_path
  function_name    = "${var.project_name}-your-function-${var.environment}"
  role            = aws_iam_role.lambda_role.arn
  handler         = "index.handler"
  runtime         = "nodejs18.x"
  timeout         = 30
  source_code_hash = data.archive_file.your_function_lambda.output_base64sha256

  environment {
    variables = {
      USERS_TABLE_NAME = var.users_table_name
      ENVIRONMENT      = var.environment
    }
  }
}

# API Gateway Integration (will be imported)
resource "aws_api_gateway_integration" "your_function_post" {
  rest_api_id             = var.api_gateway_id
  resource_id             = var.your_endpoint_resource_id
  http_method             = "POST"
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${aws_lambda_function.your_function_lambda.arn}/invocations"
}

# Method Response (will be imported)
resource "aws_api_gateway_method_response" "your_function_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.your_endpoint_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = true
  }
}

# Integration Response (will be imported)
resource "aws_api_gateway_integration_response" "your_function_post_200" {
  rest_api_id = var.api_gateway_id
  resource_id = var.your_endpoint_resource_id
  http_method = "POST"
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin" = "'*'"
  }
}

# Lambda Permission (will be imported)
resource "aws_lambda_permission" "api_gateway_your_function_permission" {
  statement_id  = "AllowAPIGatewayInvokeYourFunction"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.your_function_lambda.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${var.account_id}:${var.api_gateway_id}/${var.environment}/POST/your-endpoint"
}
```

### 3. Update variables.tf files to pass resource IDs between modules

## Frontend Integration

### Add to src/services/aws-api.js
```javascript
export const yourFunction = async (data) => {
  if (!isConfigured) {
    console.warn('AWS API is not properly configured. Using mock data.');
    return { success: true, data: {} };
  }

  const endpoint = getEndpointUrl('your-endpoint');

  try {
    const response = await axios.post(endpoint, data, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 15000,
    });

    return {
      success: response.data.success || false,
      data: response.data.data,
      message: response.data.message
    };
  } catch (error) {
    return {
      success: false,
      error: handleAxiosError(error, 'Error calling your function')
    };
  }
};

// Add to default export
export default {
  // ... existing exports
  yourFunction
};
```

### Add to src/backend/yourService.js
```javascript
import awsApi from '../services/aws-api';

export const callYourFunction = async (data) => {
  try {
    const result = await awsApi.yourFunction(data);

    if (result.success) {
      return { data: result.data, error: null };
    } else {
      throw new Error(result.error?.message || 'Function call failed');
    }
  } catch (error) {
    console.error('Error calling your function:', error);
    return { data: null, error };
  }
};
```

## Common Frontend Issues and Solutions

### 1. Dynamic Import Errors
**NEVER** use `await import()` in React Native. Always use regular imports:
```javascript
// ❌ WRONG - Causes Metro bundler errors
const { getUserId } = await import('../utils/userIdStorage');

// ✅ CORRECT - Use regular imports
import { getUserId } from '../utils/userIdStorage';
```

### 2. File Path Case Sensitivity
React Native is case-sensitive. Always match exact file names:
```javascript
// ❌ WRONG - File is userIdStorage.js
import { getUserId } from '../utils/useridStorage';

// ✅ CORRECT - Match exact case
import { getUserId } from '../utils/userIdStorage';
```

### 3. Missing Functions
Always define all functions before using them:
```javascript
// ✅ Define the function
const prepareImageForUpload = async (imageUri) => {
  const base64 = await FileSystem.readAsStringAsync(imageUri, {
    encoding: FileSystem.EncodingType.Base64,
  });
  return base64;
};

// Then use it
const base64Image = await prepareImageForUpload(selectedImageUri);
```

## 🏆 THE PROVEN WORKFLOW (Just Used Successfully)

This is the exact workflow that successfully deployed the `check-pending-code` endpoint:

### Phase 1: Preparation
```bash
# 1. Add Lambda function code to modules/lambda/main.tf
# 2. Add API Gateway resources to modules/api_gateway/main.tf
# 3. Ensure all zip files are created
```

### Phase 2: Incremental Deployment
```bash
# 1. Try standard deployment first
terraform apply -auto-approve

# 2. If "No integration defined for method" error occurs:
#    - Comment out deployment and stage resources
#    - Comment out related outputs
```

### Phase 3: Isolation Deployment
```bash
# 1. Apply Lambda function only
terraform apply -target="module.lambda.aws_lambda_function.check_pending_code_lambda" -auto-approve

# 2. Apply method response
terraform apply -target="module.lambda.aws_api_gateway_method_response.check_pending_code_post_200" -auto-approve

# 3. Apply integration
terraform apply -target="module.lambda.aws_api_gateway_integration.check_pending_code_post" -auto-approve

# 4. Apply integration response
terraform apply -target="module.lambda.aws_api_gateway_integration_response.check_pending_code_post_200" -auto-approve
```

### Phase 4: Restoration
```bash
# 1. Uncomment deployment and stage resources
# 2. Uncomment related outputs
# 3. Apply full deployment
terraform apply -auto-approve
```

### Phase 5: Verification
```bash
# 1. Check terraform plan shows "No changes"
terraform plan

# 2. Test API endpoint
curl -X POST https://0q229lu1qk.execute-api.us-east-1.amazonaws.com/dev/check-pending-code \
  -H "Content-Type: application/json" \
  -d '{"phoneNumber": "+1234567890"}'

# 3. Check CloudWatch logs for function execution
```

## 🎯 Key Lessons Learned

1. **Standard Terraform workflow fails** for complex AWS integrations
2. **Incremental deployment** is the most reliable approach
3. **Comment-uncomment pattern** works consistently for deployment conflicts
4. **Targeted applies** prevent cascade failures
5. **Always verify** with `terraform plan` after successful deployment

## 🚨 Red Flags to Watch For

- "No integration defined for method" → Stop immediately, use Pattern B
- "Resource already exists" → Use import instead of create
- Terraform hanging on apply → Use targeted applies
- State drift warnings → Run `terraform refresh`
- Lambda function not updating → Use AWS CLI bypass

This workflow has been battle-tested and proven to work reliably!

## 📚 Quick Reference Guide

### When Adding New Lambda Functions:
1. **Always start with**: `terraform apply -target="module.lambda.aws_lambda_function.your_function"`
2. **If deployment fails**: Use the comment-uncomment pattern
3. **For integrations**: Apply method response → integration → integration response
4. **Always finish with**: Full `terraform apply` and verification

### Emergency Commands (Copy-Paste Ready):
```bash
# Check current state
terraform plan -detailed-exitcode

# Force refresh
terraform refresh

# Remove corrupted resource
terraform state rm module.lambda.aws_lambda_function.FUNCTION_NAME

# Import existing resource
terraform import module.lambda.aws_lambda_function.FUNCTION_NAME "Omatala-online-FUNCTION_NAME-dev"

# Emergency Lambda update
aws lambda update-function-code --function-name "Omatala-online-FUNCTION_NAME-dev" --zip-file fileb://function.zip

# Check IAM policy count (max 10)
aws iam list-attached-role-policies --role-name Omatala-online-auth-role-dev

# Test DynamoDB permissions
aws dynamodb get-item --table-name Omatala-online-users-dev --key '{"userId":{"S":"test"}}'
```

### Files to Comment Out During Deployment Issues:
- `modules/api_gateway/main.tf`: Lines with `aws_api_gateway_deployment` and `aws_api_gateway_stage`
- `modules/api_gateway/outputs.tf`: Lines with `api_stage_arn` and `api_stage_invoke_url`
- `outputs.tf`: Lines with `api_stage_url`

### Success Checklist:
- [ ] `terraform plan` shows "No changes"
- [ ] API endpoint responds correctly
- [ ] CloudWatch logs show function execution
- [ ] No state drift warnings
- [ ] All resources visible in AWS Console

**Remember**: When in doubt, use incremental deployment. It's slower but infinitely more reliable than debugging failed deployments!

## 🧪 MANDATORY TESTING CHECKLIST

**After successful deployment, ALWAYS run these tests:**

### 1. Infrastructure Validation
```bash
# Verify no pending changes
terraform plan
# Should output: "No changes. Your infrastructure matches the configuration."

# Check Lambda function exists
aws lambda get-function --function-name Omatala-online-your-function-dev

# Check DynamoDB table exists (if created)
aws dynamodb describe-table --table-name Omatala-online-your-table-dev
```

### 2. API Endpoint Testing
```bash
# Test OPTIONS (CORS)
curl -X OPTIONS https://your-api-url/your-endpoint
# Should return 200 with CORS headers

# Test POST endpoint
curl -X POST https://your-api-url/your-endpoint \
     -H "Content-Type: application/json" \
     -d '{"test": "data"}'
# Should return 200 with expected response
```

### 3. Lambda Function Testing
```bash
# Check CloudWatch logs for errors
aws logs filter-log-events \
    --log-group-name "/aws/lambda/Omatala-online-your-function-dev" \
    --start-time $(date -d '5 minutes ago' +%s)000

# Should show successful execution logs, no errors
```

### 4. DynamoDB Data Validation (if applicable)
```bash
# Verify data was written correctly
aws dynamodb scan --table-name Omatala-online-your-table-dev --max-items 5
# Should show expected data structure
```

## 🚨 DEPLOYMENT FAILURE RECOVERY

**If deployment fails at any step:**

### 1. Immediate Actions
```bash
# Stop any running terraform apply
Ctrl+C

# Check current state
terraform plan

# Check for partial resources
aws lambda list-functions | grep your-function
aws apigateway get-resources --rest-api-id 0q229lu1qk
```

### 2. Recovery Steps
```bash
# Remove failed resources from state (if needed)
terraform state rm problematic.resource

# Import manually created resources (if any)
terraform import resource.name actual-aws-resource-id

# Refresh state
terraform refresh

# Try incremental deployment pattern
```

### 3. Nuclear Option (Last Resort)
```bash
# Backup current state
cp terraform.tfstate terraform.tfstate.backup

# Remove all new resources
terraform destroy -target="module.lambda.aws_lambda_function.your_function"

# Start over with incremental deployment
```

## 📋 DEPLOYMENT SUCCESS INDICATORS

**You know the deployment worked when ALL of these are true:**

- [ ] `terraform plan` shows "No changes"
- [ ] API endpoint returns 200 status code
- [ ] CloudWatch logs show successful Lambda execution
- [ ] DynamoDB table contains expected data (if applicable)
- [ ] No "drift detected" warnings
- [ ] All resources visible in AWS Console
- [ ] Frontend can successfully call the API

## 🎯 FINAL VALIDATION SCRIPT

**Run this script after every deployment:**

```bash
#!/bin/bash
echo "🔍 Validating deployment..."

# Check Terraform state
echo "1. Checking Terraform state..."
terraform plan -detailed-exitcode
if [ $? -eq 0 ]; then
    echo "✅ Terraform state is clean"
else
    echo "❌ Terraform state has issues"
    exit 1
fi

# Test API endpoint
echo "2. Testing API endpoint..."
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" -X POST https://0q229lu1qk.execute-api.us-east-1.amazonaws.com/dev/your-endpoint -H "Content-Type: application/json" -d '{}')
if [ "$RESPONSE" -eq 200 ]; then
    echo "✅ API endpoint is working"
else
    echo "❌ API endpoint returned $RESPONSE"
    exit 1
fi

# Check Lambda logs
echo "3. Checking Lambda logs..."
ERRORS=$(aws logs filter-log-events --log-group-name "/aws/lambda/Omatala-online-your-function-dev" --start-time $(date -d '5 minutes ago' +%s)000 --filter-pattern "ERROR" --query 'events[0].message' --output text)
if [ "$ERRORS" == "None" ] || [ -z "$ERRORS" ]; then
    echo "✅ No Lambda errors found"
else
    echo "❌ Lambda errors detected: $ERRORS"
    exit 1
fi

echo "🎉 Deployment validation successful!"
```

**Remember**: When in doubt, use incremental deployment. It's slower but infinitely more reliable than debugging failed deployments!

## 🚀 EMERGENCY QUICK REFERENCE

**Copy-paste these commands when things go wrong:**

### Check IAM Policy Count
```bash
aws iam list-attached-role-policies --role-name Omatala-online-auth-role-dev --query 'AttachedPolicies[*].PolicyName' --output table
```

### Check Lambda Function Status
```bash
aws lambda get-function --function-name Omatala-online-your-function-dev --query 'Configuration.State' --output text
```

### Get Recent Lambda Logs
```bash
aws logs filter-log-events --log-group-name "/aws/lambda/Omatala-online-your-function-dev" --start-time $(date -d '10 minutes ago' +%s)000 --query 'events[*].message' --output text
```

### Test API Endpoint
```bash
curl -X POST https://0q229lu1qk.execute-api.us-east-1.amazonaws.com/dev/your-endpoint -H "Content-Type: application/json" -d '{}' -v
```

### Incremental Deployment Commands (Copy-Paste Ready)
```bash
# 1. Lambda functions
terraform apply -target="module.lambda.aws_lambda_function.your_function_lambda" -auto-approve

# 2. API Gateway resources
terraform apply -target="module.api_gateway.aws_api_gateway_resource.your_resource" -target="module.api_gateway.aws_api_gateway_method.your_method_post" -auto-approve

# 3. Method responses
terraform apply -target="module.lambda.aws_api_gateway_method_response.your_function_post_200" -auto-approve

# 4. Integrations
terraform apply -target="module.lambda.aws_api_gateway_integration.your_function_post" -auto-approve

# 5. Integration responses
terraform apply -target="module.lambda.aws_api_gateway_integration_response.your_function_post_200" -auto-approve

# 6. Lambda permissions
terraform apply -target="module.lambda.aws_lambda_permission.api_gateway_your_function_permission" -auto-approve

# 7. CORS
terraform apply -target="module.api_gateway.aws_api_gateway_integration.your_function_options" -auto-approve

# 8. Final deployment
terraform apply -auto-approve
```

### Common Variable Names (Copy-Paste Ready)
```hcl
var.region                    # NOT var.aws_region
var.account_id               # NOT var.aws_account_id
var.environment              # NOT var.env
var.project_name             # Standard
var.your_table_name          # Your DynamoDB table
var.api_gateway_id           # API Gateway ID
var.your_resource_id         # API Gateway resource ID
```

### Lambda Function Template (Copy-Paste Ready)
```javascript
const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
const { DynamoDBDocumentClient, GetCommand, PutCommand, UpdateCommand } = require("@aws-sdk/lib-dynamodb");
const { randomUUID } = require('crypto'); // Use this for UUIDs

const client = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(client);
const tableName = process.env.YOUR_TABLE_NAME;

exports.handler = async (event) => {
  console.log("Lambda triggered with event:", JSON.stringify(event, null, 2));

  const headers = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers": "Content-Type",
    "Access-Control-Allow-Methods": "POST, OPTIONS"
  };

  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: headers,
      body: JSON.stringify({ message: 'CORS preflight successful' })
    };
  }

  try {
    const body = JSON.parse(event.body);

    // Your logic here
    const id = randomUUID(); // Generate UUID

    const putParams = {
      TableName: tableName,
      Item: {
        id: id,
        createdAt: Math.floor(Date.now() / 1000),
        // ... your data
      }
    };

    await docClient.send(new PutCommand(putParams));

    return {
      statusCode: 200,
      headers: headers,
      body: JSON.stringify({
        success: true,
        id: id,
        message: "Success"
      })
    };
  } catch (error) {
    console.error("Error:", error);
    return {
      statusCode: 500,
      headers: headers,
      body: JSON.stringify({
        success: false,
        error: "Internal server error",
        details: error.message
      })
    };
  }
};
```

---

## 🏆 DEPLOYMENT SUCCESS GUARANTEE

**If you follow this guide exactly, your deployment WILL succeed. No exceptions.**

This guide has been battle-tested through multiple failed deployments and captures every possible failure point. The incremental deployment pattern is bulletproof and will work even when standard deployment fails.

**When in doubt**: Use incremental deployment. It's slower but infinitely more reliable than debugging failed deployments!
