# Complete Feed Algorithm Behavior Explanation

This document provides a comprehensive explanation of how the marketplace feed algorithm works, covering all scenarios, edge cases, and expected behaviors in plain English.

-This document i compiled with AI, through back and forth,just to make sure i capture the most detail as possible and have it structured in a better way, but as we implement, we will still iterate. But these documents will help you understand what i am trying to get implemented.

## Core Algorithm Philosophy

The feed algorithm is built around one fundamental principle: * **minimize the overlap of unseen items across users in each batch** { Every item must be seen at least once (global goal), Every user must see every item (individual goal), System adapts   when users are inactive (dynamic rebalancing),Minimum intersection applies throughout (not just initially) }

## How the Basic Distribution Works

### The Minimum Intersection Strategy

Imagine you have 5 items [A, B, C, D, E] and 3 users. Instead of showing all users the same sequence (A, B, C, D, E), which would mean items A and B get seen by everyone before items D and E get seen by anyone, the algorithm distributes items strategically:

- User 1 sees: A, B, C, D, E
- User 2 sees: C, D, E, A, B  
- User 3 sees: E, A, B, C, D

This way, in the first batch of 2 items:
- User 1 sees: A, B
- User 2 sees: C, D
- User 3 sees: E, A

Now items A, B, C, D, E have all been seen at least once across all users, achieving maximum fairness with minimum overlap.

### Scaling to Real Numbers

With 50,000 items and 1,000 users, the algorithm works the same way. If each user sees 10 items per batch, then after distributing items strategically across all active users, every single item will have been seen at least once. After this milestone is reached, the algorithm continues with the same minimum intersection principle to ensure each individual user eventually sees all 50,000 items, but it distributes the remaining items across users in a way that minimizes overlap in each batch round.

## Pre-computed Sequences vs Real-time Calculation

### Why Pre-computation is Essential

Calculating the optimal distribution for thousands of users and items in real-time would be too slow. Instead, the system pre-computes personalized sequences for each user that collectively achieve the minimum intersection goal.


### Caching Strategy

These sequences are stored in a fast cache (like Redis) so when a user scrolls through their feed, the app simply reads the next items from their pre-computed sequence rather than calculating anything in real-time.

## Handling Dynamic Changes

### When Items are Turned On/Off

When a seller toggles their item off:
- The item immediately disappears from all future feed requests
- Users who haven't seen it yet simply skip over it in their sequence
- The global "seen at least once" tracking remains unchanged

When a seller toggles their item back on:
- The item re-enters all users' sequences at the appropriate position
- Users who haven't seen it will see it in their normal sequence flow
- Users who already saw it won't see it again until they've seen all other items

### When New Items are Added

New items can join the system in two ways:

**Normal Priority**: New item gets added to the end of all users' sequences. Users will see it after they've seen all previously existing items.

**High Priority**: If the new item's seller has very few subscription days left (fewer than items the user hasn't seen yet), the new item gets inserted earlier in the user's sequence to ensure it gets seen before the subscription expires.

## User Activity Patterns and Dynamic Rebalancing

The algorithm has a dual objective:
1. **Phase 1**: Ensure every item is seen at least once by someone (global fairness)
2. **Phase 2**: Ensure every user sees every item (individual completeness)

Both phases use the same minimum intersection strategy to distribute items fairly across users, but Phase 1 prioritizes global item exposure while Phase 2 focuses on individual user journey completion.

### The Inactive User Problem

If User 1 gets a sequence but never logs in, their "reserved" items in positions 1-10 never get seen on time, meaning there items could be at positions 25 000 in all users, hence our system must be dynamic in this nature that, a user has their sequence as per initial rules, but the moment we see that other items that are suppose to be seen by the other user havent been seen, its better to bring them close in the sequence, so that we archieve the global fairnes property. Also we can have 5 users on the platform, meaning we have set up the sequence such that each item is seen atleast once, but if one user starts scrolling, then this means that user sees all items. But what if two users are viewing and the other three are not viewing. This means the system should andjust to still distribute the unseen items that were supposed to be seen by the offline users, but we still do it in such a way that we have minimum overlap for the batch.

### Adaptive Rebalancing Solution
-This is the current solution we have for the above problem, but if there is a better approach, we will find it as we itterate.

Every 10 minutes, a background process checks:
1. Which users haven't been active in the last 24 hours
2. Which items were "reserved" for these inactive users in upcoming batches but never seen (this will be the situation when we have a very large number of items)
3. Redistributes these unseen items among currently active users using the same minimum intersection strategy. (This was explained above too)

This happens transparently and continuously - active users receive items that would have gone to inactive users, ensuring no item gets stuck waiting for an inactive user to return. The system maintains the core principle: every item must be seen at least once, and every user must eventually see every item.

### Returning User Behavior

When a user returns after being inactive:
- They continue from where they left off, but their sequence may have been updated in the background to account for items that were redistributed while they were away,this is mostly just a rearangement to make sure each item is seen atleast once.
- The algorithm ensures they will still see all items they haven't seen yet
- Items they "missed" while inactive have been redistributed to other users to maintain the "seen at least once" guarantee
- No disruption to user experience - they simply continue scrolling from their last position

## Guest User Integration

### Guest Identity Management

Guest users get a unique identifier stored on their device in a way they cannot manually delete (AsyncStorage). This identifier persists even if they clear the app cache or restart their phone.

### Guest Lifecycle

1. **First Visit**: Guest gets assigned a unique ID and a pre-computed sequence just like registered users
2. **Continued Use**: Guest views are tracked and count toward the global "seen at least once" tracking
3. **Inactivity**: After 20 days of no app usage, the guest account expires
4. **Return Before Expiry**: If guest returns within 20 days, their expiry extends by another 20 days
5. **Clean Expiry**: After 20 days of inactivity, all guest data is automatically deleted

### Guest Impact on Algorithm

Guest views are treated exactly the same as registered user views for the purpose of item distribution. If a guest user sees Item A, then Item A counts as "seen at least once" globally, contributing to the fairness algorithm.

### Guest ID Validation

Every time a user select guest option from phone number screen:
1. App checks if the stored guest ID is still valid on the backend
2. If expired or invalid, app generates a new guest ID
3. If valid, guest continues to GuestFeedScreen as normal and continue with their existing sequence and view history

## Edge Cases and Special Scenarios

### All Users Caught Up

When every user has seen every available item:
1. Users see "You're all caught up!" message
2. System continues monitoring for new items
3. When new items appear, normal distribution resumes
-Note that even when user has seen all items, when they restart the app, even when there are no new items, we still resycle what they have already seen, so long as the item is valid to be seen, and we still do it in such a way that we minimise the the intersection per batch and making sure that not a single item is overly seen, unless, that item is the only one or belongs to a small group of the only items that are available to be seen. {Reason: recycling items like this makes sure the items are always exposed to a viewer, and in the case of recycling,when  have a full recycle we still output youre all caught up, so long there are no new items to show}

### Subscription Expiry During Viewing

If a seller's subscription expires while users are viewing their items:
1. Item immediately becomes inactive
2. Users skip over it in their sequences
3. No error or disruption to the user experience
4. If seller renews subscription, item re-enters the distribution pool


### Mass User Onboarding

1. With keeping the core fucntionalities of the system, new users must be able to see items immediately so long as the global state of the available items is not : no items to show. Hence what we could do is, we deliver the batch in real time, while in the background process, we are calcutating the sequence for that user, and once it is done, we auto swicth to it, an just like that the new user enters the system and to the system it will be like a user who has been already there. Remember in the backgroung process, we are still sticking to the minimum overlap and also to make sure each item is viewed atleast once, and each user eventually sees each item.
3. Background process continues optimizing distribution
4. System maintains fairness even under high load

### Data Consistency

All view tracking, sequence management, and global state updates happen atomically to prevent race conditions when multiple users are active simultaneously.

## Cache and Performance Management

### Background Jobs

- **Every 5 minutes**: Update eligible items cache (items from sellers with active subscriptions) This is not very final, we could iterate on it as we progress further.
- **Every 10 minutes**: Rebalance sequences for inactive users
- **Daily at midnight**: Clean up expired guest accounts and optimize cache storage


### Fallback Behavior

If the cache fails or becomes unavailable:
1. System falls back to direct database queries
2. Performance may be slower but functionality remains intact
3. Cache rebuilds automatically once the issue is resolved

## Expected User Experience

### Normal Flow {This flow only i  can test it and see if it behaves as i want or not}

1. User opens app and scrolls feed
2. Sees items in their personalized sequence
3. Items appear diverse and interesting (due to minimum intersection strategy)
4. Never sees the same item twice until they've seen everything
5. Eventually reaches "all caught up" state
6. Gets notified when new items become available

### Guest Experience {This flow only i  can test it and see if it behaves as i want or not}

1. Opens app without registering, from phone number screen
2. Gets full feed experience identical to registered users
3. Their views contribute to global fairness
4. Can use app for weeks with consistent experience
5. Data automatically cleans up after extended inactivity, so that we dont have a guestID online but the guest will never return, or maybe they have deleted the mobile app and they we just testing. Thats why we need a clean up.

This algorithm ensures mathematical fairness, handles real-world usage patterns, performs well under load, and provides an excellent user experience for both registered and guest users.